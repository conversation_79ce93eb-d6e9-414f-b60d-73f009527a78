{"a_shares": "A Shares", "aboutUs": "About Us", "acceptTerms": "Please accept the terms of service and privacy policy", "account": "Account", "accountAddress": "Account Address", "accountAddressRequired": "Account address is required", "accountApprovalStatus": "How to Check Account Approval Status", "accountBalance": "Account <PERSON><PERSON>", "accountGuide": "Account Guide", "accountMarketTableRowTitle1": "Name/Code", "accountMarketTableRowTitle2": "Order Price", "accountMarketTableRowTitle3": "Filled | Total", "accountMarketTableRowTitle4": "Direction | Status", "accountMarketTableTitle1": "Current Positions", "accountMarketTableTitle2": "Trade Details", "accountMarketTableTitle3": "Order Details", "accountOpeningTime": "Account Opening Duration", "accountRequirements": "Required Documents for Account Opening", "accountType": "Account Type", "activity": "Activity", "activity_reward": "Increase In Interest", "activityContent": "Activity Content", "actualAmount": "Actual Received Amount", "actualPaymentAmount": "Actual Payment Amount", "actualPaymentAmount2": "Actual Payment Amount", "add_contract_cash_amount": "Additional Contract", "add_futures_margin": "Add futures margin", "add_shares": "Add", "addAccount": "Add Account", "addBank": "Add Bank", "addBankCard": "Please add a bank card", "addedSuccessfully": "Added successfully", "addToFavorite": "Add to Favorites", "addWarning": "Add Warning", "admin_adjust_reduce": "Admin balance reduction adjustment", "admin_balance_adjustment": "Admin balance adjustment", "admins": "Admins", "afterChangeAmount": "Post-change Amount", "agentLevel": "Agent Level", "agentLevelDescription": "Agent Level Description", "aiAnalysis": "AI", "aiAnswerLabel": "Answer: ", "aiInputPlaceholder": "Enter the content to be analyzed", "aiIntelligentAnalysis": "AI Intelligent Analysis", "aiQuestionLabel": "Question: ", "aiSlogan": "Smart AI helps you manage your finances easily\nOpening a new era of smart investment", "aiThinking": "Thinking", "alert": "Warning", "all": "All", "already_arrived": "Already Arrived", "already_refunded": "Already Refunded", "alreadyHaveAnAccount": "Already have an account?", "alreadySignedIn": "Signed In", "amountRange": "Amount Range", "amountRangeError": "Amount must be between {} and {}", "amplitude": "Amplitude", "applicationRecords": "Application Records", "apply_records": "Apply Records", "applyContract": "Apply for Stock Allocation", "applyContract2": "Stock Allocation", "applyContractInterestRebate": "Apply for Contract Interest Rebate", "applyFor": "Apply for", "applyTime": "Apply time", "appName": "GP PRE", "approved": "Approved", "ask": "Ask", "audio": "Audio", "authVerification": "Identity Verification", "authVerificationSuccess": "Auth Verification Success", "autoCancel": "Auto-cancel expired contracts", "autoRenewal": "Auto Renewal", "available": "Available", "available_balance": "Available Balance", "available_margin": "Available Margin", "availableBalance": "Available Balance", "availableToBuy": "Available To Buy", "availableToClose": "Close", "availableToOpen": "Available To Open", "availableToSell": "Available To Sell", "avatar": "Avatar", "averageBuyPrice": "Average Buy Price", "averagePrice": "Avg. <PERSON>", "averageSellPrice": "Average Sell Price", "backToLogin": "Back to login", "balance": "Balance", "bank": "Bank", "bankAddedSuccessfully": "Bank added successfully", "bankCard": "Bank Card", "bankCardRecharge": "Bank Card Recharge", "bankCardWithdraw": "Bank Card Withdrawal", "bankDeposit": "Bank Deposit", "bankReservedMobile": "Bank Reserved Mobile", "basicOverview": "Basic Overview", "bearish": "bearish", "beforeChangeAmount": "Previous Amount", "bid": "Bid", "billion": "B", "birthday": "Birthday", "bonus": "Bonus Contract", "bonusAmount": "Bonus Amount", "bonusContract": "Bonus Stock Contract", "bonusContract2": "Bonus", "bullish": "bullish", "buy": "Buy", "buy_transaction": "Trade Buy", "buyBroker": "Buy Broker", "buyIndexCommissionRebate": "Buy index commission rebate", "buyPrice": "Buy Price", "buyTime": "Buy Time", "byDay": "By Day", "byMonth": "By Month", "byWeek": "By Week", "byYear": "By Year", "cancel": "Cancel", "cancelOrder": "Cancel Order", "cancelOrderConfirmation": "Are you sure you want to cancel this order?", "capitalInterestRate": "Capital Interest Rate", "cardholder": "Cardholder", "cardNumber": "Bank Card Number", "cash": "Cash", "cashbackAmount": "Cashback amount of this channel", "cashbackRate": "Cashback ratio of this channel", "cashOut": "<PERSON><PERSON><PERSON>", "cashOut1": "Withdraw", "certified": "Certified", "chairman": "Chairman", "change": "Change", "changeAmount": "Change Amount", "changeOriginalPassword": "Change Via Original Password", "changePasswordButton": "Change Password", "changePercent": "Change Percent", "chargeCurrency": "Charge Currency", "chargeDesc": "Charge Desc", "chart_data_error": "Chart data error", "chart_failed_to_load": "Failed to load chart", "chart_loading": "Loading chart...", "chart_no_data": "No chart data", "chart_waiting_for_data": "Waiting for chart data...", "chat": "Support", "chat_blocked_contacts": "Blocked Contacts", "chat_channels": "Channels", "chat_chats": "Service", "chat_connected": "connected", "chat_loading": "loading...", "chat_search": "Search", "chat_users": "Users", "chatGeneral": "General", "checkForUpdates": "Check for Updates", "china_futures_exchange": "China", "china_market": "China Market", "clearCache": "<PERSON>ache", "clearing": "Clearing cache...", "clearSearchHistory": "Clear Search History", "clearSuccess": "<PERSON><PERSON> cleared successfully", "click_to_collapse": "Click to Collapse", "click_to_expand": "Click to Expand", "close": "Close", "closePosition": "Close Position", "closeValue": "Close Value", "cnStocks": "CN", "code": "Code", "codeSent": "Code Sent", "commision": "commission", "commissionDetails": "Order Details", "commissionPrice": "Commission Price", "commissionRate": "Commission Rate", "companyInfo": "Company Info", "companyIntroduction": "Company Introduction", "companyLegalPerson": "Company Legal Person", "companyName": "Company Name", "companyProfile": "Profile", "companyStaff": "Company Staff", "companyWebsite": "Company Website", "completableTimes": "Completable Times", "completed": "Completed", "concept": "Concept", "confirm": "Confirm", "confirm_logout": "Are you sure you want to log out?", "confirm_terminate_contract": "Are you sure you want to terminate the current contract?", "confirmChanges": "Confirm Changes", "confirmClearCache": "Clear Cache?", "confirmClearCacheMsg": "Are you sure you want to clear the app cache?", "confirmLeaveGroup": "Are you sure you want to leave this group?", "confirmNewPassword": "Confirm New Password", "confirmPassword": "Confirm Password", "confirmPasswordCannotBeEmpty": "Confirm Password cannot be empty", "consultNow": "Consult now", "contactNumber": "Contact Number", "contract": "Contract", "contract_buy": "Trade Buy", "contract_deposit": "Additional Deposit", "contract_sell": "Trade Sell", "contract_unfreeze": "Contract unfrozen", "contract.period_1": "Daily", "contract.period_2": "Weekly", "contract.period_3": "Monthly", "contractAccount": "Contract Account", "contractDetails": "Contract Details", "contractFundWithdrawal": "Contract Fund Withdrawal", "contractMargin": "Contract <PERSON>", "contractMultiple": "Contract Multiple", "contractName": "Contract Name", "contractNetAssets": "Contract Net Assets", "contractNote": "Contract Note", "contractNumber": "Contract Number", "contractPeriod": "Contract Period", "contractRenewal": "Contract Renewal", "contractRenewalInterestRebate": "Contract renewal interest rebate", "contractRenewalStatus": "Contract Renewal Status", "contracts": "Contracts", "contractSubmittedSuccessfully": "Contract Submitted Successfully", "contractType": "Contract Type", "create_contract": "Create Contract Account", "createContract": "Create Contract", "createTime": "Create Time", "credited": "Credited", "cumulativeRebate": "Cumulative Rebate", "cumulativeRecharge": "Cumulative Recharge", "cumulativeReturn": "Cumulative Return", "cumulativeSubordinates": "Cumulative Subordinates", "current": "Current", "currentEntrustment": "Current Entrustments", "currentMobile": "Current Mobile", "currentPassword": "Current Password", "currentPhone": "Current Phone", "currentPositions": "Positions", "currentPrice": "<PERSON><PERSON><PERSON>", "customAmount": "Custom Amount", "customConversion": "Custom Conversion", "customerService": "Dedicated Support", "customMessage": "Custom Message", "daily": "Daily", "dailyDeclineExceeds": "Daily Decline Exceeds", "dailyIncreaseExceeds": "Daily Increase Exceeds", "dailyTask": "Daily Task", "dalian_futures_exchange": "<PERSON><PERSON>", "dark": "Dark", "darkTheme": "Dark Theme", "date": "Date", "day": "days", "dealHistory": "Deal History", "dealPrice": "Deal Price", "dealQuantity": "Deal Quantity", "delete": "Delete", "deletedSuccessfully": "Deleted successfully", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "deposit_bonus": "Deposit Gift", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "depositChannel": "Deposit Channel", "depositedSuccessfully": "Deposit application successful", "depositFailed": "Deposit Failed", "depositFunds": "Deposit Channels", "depositRecords": "Deposit Records", "depositWithdrawal": "Exchange", "depositWithdrawalFees": "Deposit/<PERSON>drawal <PERSON>", "detail": "Details", "details": "Details", "developing": "Function under development....", "direction": "Direction", "direction_status": "Direction|Status", "direction_time": "Direction|Time", "distanceToLiquidationLine": "Distance to Liquidation Line", "distanceToWarningLine": "Distance to Warning Line", "distFlow": "Capital Transaction Distribution", "dividend": "Dividend", "dividend_payment": "Dividend Distribution", "dividend_rate": "Dividend Rate", "documentType": "Document Type", "documentTypes": "Document Types", "done": "Done", "download_now": "Download now", "downloadList": "Download List", "edit": "Edit", "editWarning": "Edit Warning", "effective": "Effective", "email": "Email", "emailId": "Email", "emoji": "<PERSON><PERSON><PERSON>", "encounterAnyProblem": "Encounter Any Problem?", "enterAccountAddress": "Enter account Address", "enterAmount": "Please enter the amount", "enterCardholder": "Enter Cardholder", "enterCardNumber": "Enter Bank Card Number", "enterCode": "Enter Code", "enterConfirmPassword": "Please confirm your new password", "enterCurrentPassword": "Please enter your current password", "enterFeedback": "You can leave more feedback or comments here, we will optimize according to the specific feedback", "enterNewMobile": "Enter new mobile number", "enterPhone": "Enter your phone number", "enterRechargeAmount": "Enter Recharge Amount", "enterTransactionNumber": "Please Enter Transaction Number", "enterVerificationCode": "Enter verification code", "enterWithdrawAmount": "Enter Withdraw Amount", "entrust_details": "Entrustment Details", "entrustDetail": "Entrust Detail", "entrustDetails": "Orders", "entrustDirection": "Direction", "entrustmentPrice": "Entrustment Price", "entrustRevoke": "Entrust Revoke", "entrustTime": "Entrustment Time", "errorMsg": "Something went wrong", "establishmentDate": "Establishment Date", "exchange": "Exchange", "exchange_f": "exchange", "exclusiveService": "Exclusive Service Channel", "expandContractInterestRebate": "Expand contract interest rebate", "expandedContract": "Expanded Contract", "expandedMargin": "Expanded Margin", "expandMargin": "Expand Margin", "experience": "Experience Contract", "experienceContract": "Experience Stock Contract", "experienceContract2": "Experience", "f_market_awaiting_open": "Awaiting Open", "f_market_closed": "Closed", "f_market_halted": "Halted", "f_market_trading": "Trading", "failed": "Transaction failed", "failedToAddWallet": "Failed to add wallet", "failedToChangePassword": "Failed to change password", "failedToLoad": "failed to load", "failedToSendCode": "Failed to send verification code", "favorite": "Favorite", "favoriteAdded": "Added", "featuredStocks": "Featured Stocks", "feedback": "<PERSON><PERSON><PERSON>", "feedBack": "<PERSON><PERSON><PERSON>", "feedbackSubmitted": "<PERSON><PERSON><PERSON> submitted successfully", "feeStandard": "Fee Standards", "fieldRequired": "Field required", "file": "File", "filled": "Filled", "filled_total": "Filled|Total", "financial": "Financial", "floatingProfitLoss": "Floating P&L", "forced_liquidation_line": "Forced Liquidation Line", "forgotPasswordTitle": "Forgot Password", "freezeOrderAmount": "Freeze order transaction amount", "freezeOrderBuyAmount": "Freeze order buy amount", "freezeOrderBuyFee": "Freeze order buy fee", "freezeOrderFee": "Freeze order fee", "fri": "<PERSON><PERSON>", "frozen": "Frozen", "frozenAmount": "Frozen Amount", "fundAccountType": "Account Type", "fundRecords": "Funding Records", "fundRelated": "Fund Related", "fundTrend": "Fund Trend", "fundType": "Fund Type", "futures": {"applyFuturesFunding": "Apply For Futures Funding", "futuresFunding": "Futures Funding", "markets": {"china": "CN Futures"}, "types": {"bonusFutures": "Bonus Futures", "experienceFutures": "Experience Futures", "normalFutures": "Normal Futures"}}, "futures_buy_fee_rebate": "Futures buy fee rebate", "futures_sell_fee_rebate": "Futures sell fee rebate", "gainers": "Gaine<PERSON>", "gem_board": "Gem Board", "gen_board": "GEM Board", "gender": "Gender", "general": "General Contract", "generalManager": "General Manager", "generateInviteLink": "Generate Invite Link", "generateLink": "Generate Link", "get": "Get", "get_verification_method_failed": "Failed to fetch verification", "getCode": "Get Code", "getSmsCode": "Get SMS Code", "getVerificationCode": "Get Code", "giftAmount": "Gift Amount", "giveDay": "Days Given", "go_to_auth_verification": "Go verification", "graduallyRate": "Gradually Rate", "greenUpRedDown": "Green Up Red Down", "groupCreated": "Group Created", "groupNotification": "Group Notification", "hasEffectiveUser": "Has Effective User", "heldAssets": "Held Assets", "high": "High", "high_52w": "52W High", "high_price": "High", "highest": "High", "historicalMessages": "Historical Messages", "hk_shares": "HK Shares", "hkFeeStandard": "HK Stock Fee Standards", "hkSameDayTrading": "HK Stock Day Trading Available", "hkStocks": "HK", "hkStockTrading": "HK Stock Trading", "hkTradingHours": "HK Trading Hours", "holding": "Holding Time", "holding_days": "Holding Days", "home": "Home", "homeH1Title": "Smart Investment", "homeH2Title": "Mission", "homeH3Title": "Benefits", "homeH4Title": "Support", "homeH5Title": "About Us", "hot_events": "Hot Events", "hot_stocks": "Hot Stocks", "hotActivities": "Hot Activities", "hotStocks": "Hot Stocks", "id": "ID", "idBack": "Certificate Back", "idCard": "ID Card", "idFront": "Certificate Front", "idNumber": "Certificate Number", "ifYouEncounterAnyProblems": "If you encounter any problems, please contact us", "image": "Image", "in": "In", "incorrectOtp": "Please enter a valid verification code", "indexTradingTip": "Stock index trading tips: Current 1 stock index point = {} times", "industry": "Industry", "industry_sectors": "Industry Sectors", "initialMargin": "Initial Margin", "instructions": "Instructions", "insufficientBalance": "Insufficient Balance", "interest": "Interest Coupon", "interest_deduction": "Interest Deduction", "interest_type_1": "Manual Deposit", "interest_type_2": "Check-in Bonus", "interest_type_3": "Event Bonus", "interest_type_4": "Contract Account Application", "interest_type_5": "Interest Deduction", "interest_type_6": "Deposit Bonus", "interest_type_7": "Manual Withdrawal", "interestAmount": "Interest Amount", "interestCoupon": "Interest Coupon", "interestCouponAmount": "Interest Coupon Amount of this channel ", "interestDiscount": "Interest Discount", "interestRate": "Interest Rate", "interestRateInterest": "Interest Rate / Interest", "interestRateRatio": "Interest Rate Ratio", "interestRatio": "Interest Ratio", "interestRebateRate": "Interest Rebate Rate", "interestRecords": "Interest Voucher Record", "interestReduceAmount": "Interest Waiver Amount", "interestRefundRate": "Interest Refund Rate", "invalid_address": "Invalid address", "invalidCustomMessage": "Invalid Custom Message", "invalidMobileNumber": "Invalid mobile number", "invalidPassword": "Invalid password", "invitationRebate": "Invite", "inviteAndEarn": "Invite And Earn", "inviteAndEarnDescription": "Invite friends and earn commission", "inviteCode": "Invite Code", "inviteCodeCopiedToClipboard": "Invite Code Copied To Clipboard", "inviteFriends": "Invite Friends (with recharge)", "inviteLinkCopiedToClipboard": "Invite Link Copied To Clipboard", "inviteRebate": "Invite Rebate", "inviteUpgrade": "Quick upgrade through invitation", "isHelpful": "Does the above content solve your problem?", "kickedOfflineMessage": "You have been logged out. Please login again.", "kline_15min": "15-Min<PERSON>", "kline_1min": "1-<PERSON><PERSON>", "kline_30min": "30-Minute", "kline_5min": "5-Min<PERSON>", "kline_day": "Daily K-Line", "kline_month": "Monthly K-Line", "kline_week": "Weekly K-Line", "kline_year": "Yearly K-Line", "language": "Language", "large_order": "Large", "latest_price": "latest price", "latestActivities": "Latest Activities", "latestPrice": "Latest Price", "leading_concept": "Leading Concept", "leading_industry": "Leading Industry", "leadingStocks": "Leading", "leave": "Leave", "leftGroupSuccess": "You have successfully left the group", "levelingUpToEarnMore": "Leveling up to earn more", "leverage": "Leverage", "leverage_f": "leverage", "light": "Light", "lightTheme": "Light Theme", "limitOrder": "Limit Order", "line_selection": "Select Line", "liquidationAmount": "Liquidation Amount", "liquidationLine": "Liquidation Line", "loadError": "Load failed!", "loadFailed": "Load failed!", "loading": "Loading", "location": "Location", "login": "<PERSON><PERSON>", "loginAccount": "Password Login", "loginAccountHint": "Enter Account", "loginAccountPhone": "OTP Login", "loginAgreementAnd": "and", "loginAgreementPrivacy": "Privacy Policy", "loginAgreementTerms": "Terms of Service", "loginAgreementText": "By logging in, you agree to the", "loginForgotPassword": "Forgot Password?", "loginHeader": "Pursue Excellence Achieve the Future", "loginLoginNow": "Login Now", "loginNoAccount": "Don't have an account?", "loginPassword": "Enter Password", "loginPhone": "Enter Phone Number", "loginPhonePlaceholder": "Enter Phone Number", "loginRegisterNow": "Register Now", "loginRememberPassword": "Remember Password", "loginRequired": "<PERSON><PERSON> Required", "loginSuccess": "Login Success", "loginVerificationCode": "Get Verification Code", "loginVerificationCodePlaceholder": "Enter Verification Code", "logout": "Logout", "longTerm": "Long Term", "losers": "Losers", "lossReplenishment": "Loss Replenishment", "lossWarningLine": "Loss Warning Line", "lot_size": "Lot Size", "lotForSecurities": "Lot", "lotForStockIndex": "Board Lot", "lottery": "Lottery Contract", "low": "Low", "low_52w": "52W Low", "low_price": "Low", "lowest": "Low", "main_board": "Main Board", "mainBusiness": "Main Business", "manageNumber": "Manage Number", "margin": "<PERSON><PERSON>", "margin_alert_level": "<PERSON><PERSON>", "margin_amount": "<PERSON><PERSON> Amount", "margin_calculation": "margin calculation", "margin_increase": "Margin Increase", "margin_per_lot": "margin per lot", "margin_ratio": "Margin ratio", "marginAgreement": "Margin Trading Agreement", "marginCall": "<PERSON><PERSON>", "market": "Market", "market_cap": "Market Cap", "marketCN": "CN", "marketHK": "HK", "marketOrder": "Market Order", "marketPrice": "Market Price", "marketType": "Market Type", "marketUS": "US", "marketValue": "Market Value", "maturitySettlement": "Maturity Settlement", "maximum_trade": "maximum trade", "maximumAmount": "Maximum amount", "medium_order": "Medium", "memberName": "Member Name", "members": "Members", "memberStatus": "Member Status", "mergedMessage": "Merged Message", "million": "M", "mineWarning": "Mine Warning", "minimum_trade": "minimum trade", "minimumAmount": "Minimum amount", "minimumAmountHint": "Enter amount (min: 500)", "minimumTradingUnit": "Minimum Trading Unit", "minutes": "Minutes", "missionAward": "Mission Award", "missionCenter": "Mission", "mobileUpdatedSuccessfully": "Mobile number updated successfully", "mon": "Mon", "month": "Month", "monthly": "Monthly", "more": "More", "multiple": "Multiple", "multiples": "Multiples", "myAssets": "My Assets", "myCommission": "My Commission", "myInterest": "My Earnings", "myInterestTitle": "My Earnings", "myNotifications": "My Notifications", "myRebateData": "My Rebate Data", "name": "Name", "name_code": "Name|Code", "nameOnId": "Certificate Name", "networkError": "Network error, please try again", "networkErrorBadRequest": "Invalid request parameters", "networkErrorBadResponse": "Request failed, please try again later", "networkErrorCancelled": "Request cancelled", "networkErrorCertificate": "Certificate verification failed, please check your network security settings", "networkErrorConnection": "Network connection error, please check your network settings", "networkErrorDns": "DNS resolution failed, please check your network settings", "networkErrorForbidden": "No permission to access this resource", "networkErrorMessage": "Network connection error, please check your network settings", "networkErrorNoHost": "Cannot connect to server, please try again later", "networkErrorNotFound": "The requested resource does not exist", "networkErrorParsing": "Data parsing error, please try again later", "networkErrorReceiveTimeout": "Server response timeout, please try again later", "networkErrorSendTimeout": "Request timeout, please try again later", "networkErrorServer": "Server internal error, please try again later", "networkErrorSocket": "Network connection error, please check your network settings", "networkErrorTimeout": "Connection timeout, please check your network", "networkErrorUnauthorized": "Unauthorized, please login again", "networkErrorUnknown": "An unknown error occurred, please try again later", "new_version_detected": "New version detected", "newbieQuestions": "FAQ", "newbieTask": "<PERSON><PERSON> Task", "newMobile": "New Mobile Number", "newMobileCannotBeSameAsOld": "New phone number cannot be the same as the current phone number", "newPassword": "New Password", "newPasswordCannotBeEmpty": "New Password cannot be empty", "news": "News", "newsDetails": "News Details", "nickname": "Nickname", "night_market": "night market", "no": "No", "no_available_payment_account": "No available payment account", "no_contracts": "No contracts", "no_data_available": "No Data Available", "no_entrusts": "No entrust records", "no_holdings": "No holdings records", "no_results_found": "No Results Found", "no_trades": "No trade records", "noAvailablePosition": "No Available Positions", "noExclusiveSupport": "You have no exclusive support. Please contact online support.", "noGalleryPermission": "Please enable photo permissions in settings to continue", "noMoreData": "No more data", "noMoreDataToLoad": "No more data to load", "noMoreStocksToLoad": "No more stocks to load", "noRebateDetailsFound": "No rebate details found", "noResults": "No results found", "normal": "Normal", "normalContract": "Normal Stock Contract", "normalContract2": "Normal", "noSearchHistory": "No search history", "noSubordinatesFound": "No subordinate data found", "noTasksAvailable": "No Tasks Available", "notice": "Notice", "notLoggedIn": "You are not logged in", "officeAddress": "Office Address", "ok": "OK", "online_support": "Online Support", "onlineCustomerService": "Online Customer Service", "open": "Open", "open_browser_failed": "Failed to open browser", "open_price": "Open Price", "opening_price": "Open", "openingBank": "Opening Bank", "openLong": "Open Long", "openLongSymbol": "<PERSON>", "openShort": "Open Short", "openShortSymbol": "Short", "openSymbol": "Open", "openTrade": "Position", "operate": "Operate", "order_price": "Order Price", "order_unfreeze": "Order Unfreeze", "orderAmount": "Order Amount", "orderCancelSuccess": "Order cancelled successfully", "orderConfirmation": "Order Confirmation", "orderHistory": "Commission History", "orderNumber": "Order Number", "orderPrice": "Order Price", "orderQuantity": "Order Quantity", "orderTime": "Order Time", "orderTradeSuccess": "Order traded successfully", "orderType": "Order Type", "originalPassword": "Original Password", "originalPasswordCannotBeEmpty": "Previous Password cannot be empty", "otpSent": "SMS verification code sent successfully", "out": "Out", "owner": "Owner", "passport": "Passport", "passportFront": "Passport Front", "password_account": "Modify Login Password", "password_financial": "Modify Financial Password", "passwordChangedSuccessfully": "Password changed successfully", "passwordHint": "6-16 characters of any kind", "passwordRequired": "Password is required", "passwordRequirements": "Password Requirements", "passwordRequirementsDetails": "• 6-16 characters of any kind", "passwordRequirementsHint": "• 6-16 characters", "passwordsDoNotMatch": "New Password and Confirm Password do not match", "passwordsDontMatch": "Passwords do not match", "paymentMethods": "Payment Methods", "paymentOrders": "Payment Orders", "paymentTypes": "Payment Types", "pb_ratio": "P/B Ratio", "pe_dynamic": "P/E Dynamic", "pe_static": "P/E Static", "pe_ttm": "P/E TTM", "pending": "Pending", "pending_refund": "Pending Refund", "pending_review": "Pending Review", "peopleEffectiveCount": " effective people", "percent": "%", "perDay": "Per Day", "performCashTradeToEarn": "Commissions up to 90% for stock and index trading", "period": "Period", "period_0_rate": "", "period_1_rate": "/day", "period_2_rate": "/week", "period_3_rate": "/month", "periodType": "Period Type", "permanent": "Permanent", "perMonth": "Per Month", "person": "Person", "personalInfo": "Personal Info", "phone": "Phone Number", "phoneNumber": "Phone Number", "phoneVerification": "Change Via SMS Verification", "please_enter_amount": "Please enter amount", "please_enter_margin": "Please enter amount", "please_set_tp_sl_price": "Please set TP/SL price first", "pleaseContactCustomerService": "Please Contact Customer Service", "pleaseEnterBankReservedMobile": "Please enter bank reserved mobile", "pleaseEnterCardNumber": "Please enter bank card number", "pleaseEnterIdNumber": "Please enter ID number", "pleaseEnterRequiredFields": "Please enter required fields", "pleaseEnterValidBankReservedMobile": "Please enter valid bank reserved mobile", "pleaseEnterValidCardNumber": "Please enter valid bank card number", "pleaseEnterValidIdNumber": "Please enter a valid ID number", "pleaseEnterValidMobile": "Please enter a valid mobile number", "pleaseEnterValidName": "Please enter a valid name", "pleaseEnterValidPassportNumber": "Please enter a valid passport number", "pleaseEnterValidWithdrawPassword": "Please enter a valid 6-digit fund password", "pleaseEnterWithdrawPassword": "Please enter fund password", "pleaseEnterYourName": "Please enter your name", "pleaseEnterYourNameForTransfer": "Please enter the name of the transferor", "pleaseLoginToContinue": "Please Login To Continue", "pleaseSelectClosingTime": "Please select closing time", "pleaseSelectDocumentType": "Please select document type", "pleaseUploadIdBack": "Please upload ID back", "pleaseUploadIdFront": "Please upload ID front", "pleaseUploadPassport": "Please upload passport", "plRatio": "Profit and Loss Ratio", "pls_enter_correct_card_number": "Please enter the correct card number", "pls_select_the_bank": "Please select the bank", "position_margin": "Position Margin", "position_market_value": "Position Market Value", "position_quantity": "Position Quantity", "positionManagement": "Position Management", "positionProfitLoss": "Position P&L", "preferences": "Preferences", "pressAgainToExit": "Press again to exit", "prev_close": "Prev Close", "price": "Price", "price_limit": "price limit", "price_quantity": "Price|Quantity", "priceColor": "Price Color Scheme", "priceFellTo": "Price Fell To", "priceRoseTo": "Price Rose To", "prices": "Prices", "privacyPolicy": "Privacy Policy", "processingTrade": "Processing trade...", "profile": "Profile", "profileAvatarUpdateSuccess": "Avatar updated successfully", "profitLoss": "Profit/Loss", "profitLossRatio": "Loss Ratio", "pullToLoadMore": "Pull up to load more", "pullToRefresh": "Pull down to refresh", "qrCode": "QR Code", "quantity": "Quantity", "queryTime": "Query Time", "quotation": "Quotation", "quote": "Quotes", "range_15days": "Last 15 Days", "range_3days": "Last 3 Days", "range_7days": "Last 7 Days", "range_lastMonth": "Last Month", "range_thisMonth": "This Month", "range_today": "Today", "range_yesterday": "Yesterday", "readAndAgree": "I have read and agree to the", "realNameAuthentication": "AuthN", "realtime_5day": "5-Day", "realtime_day": "Intraday", "realTimePrice": "Real-Time Price", "rebateAmount": "Rebate Amount", "rebateConditions": "Rebate Conditions", "rebateDetails": "Rebate Details", "rebateDetailsInfo": "Rebate Details Info", "rebateDetailsNotReady": "Rebate details feature is not yet available", "rebateRatio": "Rebate Ratio", "rebateType": "Rebate Type", "received": "Received", "receiver": "Receiver", "recharge": "Recharge", "rechargeAmount": "Recharge Amount", "recorded": "Recorded", "redUpGreenDown": "Red Up Green Down", "reenterPassword": "Re-enter password", "refreshing": "Refreshing...", "refreshSuccess": "Latest data updated", "refund_rejected": "Refund Rejected", "refundAmount": "Refund Amount", "refunded": "Refunded", "region": "Region", "register": "Register", "registerAccount": "Welcome to Register", "registerConfirmPassword": "Confirm Password", "registeredAddress": "Registered Address", "registeredCapital": "Registered Capital", "registerInviteCode": "Please Enter Your Invite Code (Option)", "registerInviteCodeHint": "6-16 numbers of any kind", "registerPassword": "Enter Your Password", "registerPasswordHint": "6-16 characters of any kind", "registerSuccess": "Register Success", "rejected": "Rejected", "relatedNews": "Related News", "releaseToLoad": "Release to load more", "releaseToRefresh": "Release to refresh", "removeFromFavorite": "Remove from Favorites", "renew": "<PERSON>w", "renewContract": "Renew Contract", "replenishAmount": "Add Amount", "required_margin": "Required margin", "resend": "Resend", "reset": "Reset", "resetFailed": "Failed to reset password", "resetPassword": "Reset Password", "resetSuccess": "Password reset successful", "resetting": "Resetting password...", "reviewFailed": "Review Failed", "reviewPending": "Pending Review", "reviewSuccessful": "Review Successful", "revocation": "Delegation revocation", "revoke": "Revoke", "rewardCollected": "Successfully collected", "rise_fall": "Rise/Fall", "sat": "Sat", "scanQrCodeToAdd": "Scan QR Code To Add", "search": "Search", "searchByNameOrPhone": "Name/Phone Number", "searchHistory": "Search History", "searchKeywords": "Search for issues or keywords", "searchPlaceholder": "Search Stock/Code", "searchStockOrCode": "Search stock/code", "secretary": "Secretary", "security": "System Security", "selectBankCard": "Please select a bank card", "selectChannel": "Select Channel", "selectFundAccountType": "Select Account Type", "selectFundType": "Select Fund Type", "selectOpeningBank": "Select Opening Bank", "selectPosition": "Please select a position", "selectTradingType": "Please select trading type", "selectWallet": "Select Wallet", "selectWithdrawBank": "Select Withdraw Bank", "sell": "<PERSON>ll", "sell_transaction": "Trade Sell", "sell2": "Open", "sellBroker": "<PERSON><PERSON>", "sellIndexCommissionRebate": "Sell index commission rebate", "sellLong": "<PERSON><PERSON>", "sellPrice": "<PERSON><PERSON>", "sellShort": "<PERSON><PERSON>", "sellStockCommissionRebate": "Sell stock commission rebate", "sellTime": "<PERSON><PERSON>", "sendCodeFailed": "Send Code Failed", "sendingCode": "Sending verification code...", "sendVerificationCodeFailed": "Failed to send verification code", "serialNumber": "Serial Number", "service_charge": "Service Charge", "sessionExpired": "Session expired. Please login again.", "settle_contract": "Settle Contract Account", "settleOnExpiration": "Settle on Expiration", "shanghai_energy_exchange": "Shanghai Energy", "shanghai_futures_exchange": "Shanghai", "shanghai_stock_exchange": "Shanghai", "shenzhen_stock_exchange": "Shenzhen", "signIn": "Sign In", "signin_bonus": "Sign-in Bonus", "signInActivity": "Sign-in Activity", "signInError": "User is not signed in", "signInFailed": "Daily sign-in failed", "signingIn": "Signing in for today...", "signInLogError": "Failed to load sign-in records", "signInRules": "Sign-in Rules", "signInSuccess": "Daily sign-in successful", "small_order": "Small", "sms_code_cannot_be_empty": "SMS code cannot be empty", "smsCode": "SMS Code", "solved": "Solved", "somethingWentWrong": "Something went wrong", "spotAccount": "Spot Account", "spotTrading": "Spot Trading", "standard": "Standard Contract", "star_market": "Star Market", "startConnecting": "Start connecting with your users or channels!", "status": "Status", "sticker": "<PERSON>er", "stockIndex": "Stock Index", "stockPurchaseFeeRebate": "Stock purchase fee rebate", "stocks": "Stocks", "stop_loss": "Stop Loss", "stop_loss_above_entry": "Stop-loss price must be above the opening price", "stop_loss_below_entry": "Stop loss price must be lower than opening price", "stop_loss_price": "Stop Loss Price", "stopLossLine": "Stop Loss Line", "submit": "Submit", "subordinateDetails": "Subordinate Details", "subordinateList": "Subordinate List", "success": "Submitted successfully!", "successCommission": "Successful commissioning", "sumOfMoneySold": "Total Amount Sold", "sun": "Sun", "supplementLoss": "Supplement Contract", "symbol": "Code", "systemMessage": "System Notifications", "systemSettings": "Settings", "systemTheme": "System Theme", "szFeeStandard": "SH/SZ Stock Fee Standards", "tabCapital": "Capital", "tabMarket": "Quotes", "tabNews": "News", "tabProfile": "Profile", "take_profit": "Take Profit", "take_profit_above_entry": "Take profit price must be higher than opening price", "take_profit_below_entry": "Take-profit price must be below the opening price", "take_profit_price": "Take Profit Price", "take_profit_stop_loss": "TP/SL", "taskReward": "Task Reward", "terminate": "Terminate", "terminateContract": "Terminate Contract", "termsOfService": "Terms of Service", "textTooLong": "Text is too long", "thatChannelMaximumDeposit": "Maximum <PERSON>wal", "thatChannelMinimumDeposit": "Minimum Withdrawal", "theme": "Theme", "third": "Third", "thirdPartyDeposit": "Third Deposit", "thu": "<PERSON>hu", "time": "Time", "timed": "Timed", "times": "x", "tip_pls_complete_auth": "Tip: Please complete real-name authentication", "tips_amount_invalid_format": "Invalid amount format", "title_futures": "Futures", "title_viewResults": "Results", "toBeCredited": "To Be Credited", "toBeRefunded": "To Be Refunded", "toComplete": "To Complete", "today_earnings": "Today’s P/L", "today_open": "Today's Open", "todayNews": "Today News", "todays_stock_market": "Today's Stock Market", "topUpDeposit": "Deposit/Fund", "total": "Total", "total_shares": "Total Shares", "totalAssets": "Total Assets", "totalFinance": "Total Finance", "totalMargin": "Total Trading Capital", "totalOperatingFunds": "Total Operating Funds", "totalPayment": "Total Payment", "totalPrice": "Total Price", "totalProfitAndLoss": "Total Profit and Loss", "totalQuantity": "Total Quantity", "totalRecharge": "Recharge", "totalTip": "Stock market data fluctuates in real time and may differ from the current price after submission. Please refer to the final transaction price.", "totalTradingCapital": "Total Trading Capital", "trade": "Market", "trade_detail_page": "Trade Detail Page", "trade2": "Transaction", "tradeBuy": "Trade Buy", "tradeDetails": "Trades", "tradeDirection": "Direction", "tradeHistory": "Deal History", "tradeLeverage": "Leverage", "tradeSell": "Trade Sell", "trading_fee": "Transaction Fee", "trading_hours": "trading hours", "trading_methods": "Trading Methods", "tradingAgreement": "Trading Agreement", "tradingCenter": "Trading Center", "tradingFee": "Trading Fee", "tradingHours": "Trading Hours 09:30-05:00", "tradingRebate": "Trading Rebate", "tradingRebateRate": "Trading Rebate Rate", "tradingTask": "Trading Task", "transaction_amount": "Transaction Amount", "transaction_buy_avg_price": "Average Trade Price", "transaction_failed": "Failed", "transaction_price": "Transaction Price", "transaction_success": "Success", "transactionCommission": "Transaction Commission", "transactionDetails": "Transaction Details", "transactionDetailsShort": "Txn Info", "transactionDirection": "Transaction Direction", "transactionFee": "Transaction Fee", "transactionHistory": "Transaction History", "transactionNumber": "Transaction Number", "transactionTime": "Transaction Time", "transfer_from_contract": "Transfer Out from Contract Account", "transfer_to_contract": "Transfer to Contract Account", "transfer_to_spot": "Transfer to Spot Account", "transferor": "Transferor", "trialContract": "Trial Contract", "trigger_stop_loss_message": "When the current price reaches {price}, a market stop-loss order will be triggered to sell the current position.\nExpected loss is", "trigger_take_profit_message": "When the current price reaches {price}, a market take-profit order will be triggered to sell the current position.\nExpected profit is", "tryAgain": "Try Again", "tue": "<PERSON><PERSON>", "turnover": "Turnover", "turnover_amount": "Turnover", "turnover_rate": "Turnover Rate", "type": "Type", "unfinished": "Unfinished", "unfreezeOrderAmount": "Unfreeze order transaction amount", "unfreezeOrderAmountSimple": "Unfreeze order amount", "unfreezeOrderFee": "Unfreeze order fee", "unfreezeOrderFeeSimple": "Unfreeze order fee", "unknown": "Unknown", "unknownMessage": "Unknown Message", "unrealizedPnl": "Today's Profit", "unsolved": "Unsolved", "update": "Update", "update_available": "Update available", "update_later": "Later", "update_now": "Update now", "updatedAt": "Updated At", "updatedSuccessfully": "Updated successfully", "updateFailed": "Update failed", "updateMobile": "Update Mobile Number", "updatingPassword": "Updating password...", "upgradeVipTitle": "Upgrade Conditions", "us_shares": "US Shares", "userDataNotFound": "User data not found", "usFeeStandard": "US Stock Fee Standards", "usSameDayTrading": "US Stock Day Trading Available", "usStocks": "US", "usStockTrading": "US Stock Trading", "valid_today": "Valid Today", "validityPeriod": "Validity Period", "variety": "variety", "variety_information": "variety information", "verification": "Verification", "verification_failed": "Verification failed", "verificationCode": "Verification Code", "verificationCodeRequired": "Please enter verification code", "verificationCodeSent": "Verification code sent successfully", "verificationSubmittedSuccessfully": "Verification submitted successfully", "verify": "Verify", "verifyFailed": "Please verify your identity card", "verifying": "Verifying...", "verifySuccess": "Verification successful", "video": "Video", "view": "View", "viewAll": "View All", "viewResults": "View Results", "vip": "VIP", "vipCenter": "Level Privileges", "vipUpgrade": "Upgrade VIP Level", "volume": "Volume", "waiting_payment": "Waiting Payment", "walletAddedSuccessfully": "Wallet added successfully", "warning": "Warning", "warningLine": "Warning Line", "warningList": "Warning List", "warnValue": "Warn Value", "watchList": "Watch list", "watchlist_add_error": "Failed to add to watchlist", "watchlist_add_loading": "Adding to watchlist...", "watchlist_add_success": "Added to watchlist", "watchlist_empty": "No items in watch list", "watchlist_load_error": "Failed to load watch list", "watchlist_loading": "Loading watchlist...", "watchlist_remove_error": "Failed to remove from watchlist", "watchlist_remove_loading": "Removing from watchlist...", "watchlist_remove_success": "Removed from watchlist", "watchlistButton": "Watchlist", "wed": "Wed", "week": "Week", "weekly": "Weekly", "withdraw_select_fund_type": "Select Fund Account", "withdrawableAmount": "Withdrawable amount", "withdrawal": "<PERSON><PERSON><PERSON>", "withdrawal_records": "Withdrawal Records", "withdrawal_status_cancelled": "Cancelled", "withdrawal_status_failed": "Failed", "withdrawal_status_processing": "Processing", "withdrawal_status_success": "Successful", "withdrawalAmount": "<PERSON><PERSON><PERSON> Amount", "withdrawalCalculation": "How to Calculate Withdrawable Amount", "withdrawalFailed": "<PERSON><PERSON><PERSON> failed", "withdrawalSuccessful": "Apply successful", "withdrawAmount": "Withdraw Amount", "withdrawChannel": "Withdraw Channel", "withdrawFunds": "Withdraw Funds", "withdrawnSuccessfully": "Withdrawn successfully", "withdrawPassword": "Fund Password", "x": "x", "xTimes": "x", "yes": "Yes", "zhengzhou_futures_exchange": "Zhengzhou", "usdt": "USDT", "usdtAddressManagement": "USDT Address Management", "addUsdtAddress": "Add USDT Address", "editUsdtAddress": "Edit USDT Address", "withdrawalAddress": "<PERSON><PERSON><PERSON> Address", "rechargeNetwork": "Recharge Network", "setAsDefaultUsdtAddress": "Set as Default USDT Address", "confirmDeleteUsdtAddress": "Are you sure you want to delete this USDT address? This action cannot be undone.", "noUsdtAddressAdded": "No USDT address added", "pleaseEnterWithdrawalAddress": "Please enter withdrawal address", "pleaseSelectRechargeNetwork": "Please select recharge network", "pleaseEnterWalletAddress": "Please enter wallet address", "addAddress": "Add Address", "editAddress": "Edit Address", "paste": "Paste", "addSuccess": "Added successfully", "addFailed": "Add failed", "editSuccess": "Edited successfully", "editFailed": "Edit failed", "deleteSuccess": "Deleted successfully", "deleteFailed": "Delete failed", "setSuccess": "Set successfully", "setFailed": "Set failed", "walletNotFound": "Wallet not found", "addFailedWithError": "Add failed: {}", "editFailedWithError": "Edit failed: {}", "deleteFailedWithError": "Delete failed: {}", "setFailedWithError": "Set failed: {}"}