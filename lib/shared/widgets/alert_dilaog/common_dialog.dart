import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

class CommonDialog {
  final BuildContext context;
  final String? title;
  final String? content;
  final VoidCallback? complete;
  final String? cancelBtnTitle;
  final String? sureBtnTitle;
  final TextStyle? sureBtnTitleStyle;
  final bool showCancelBtn;

  CommonDialog(
    this.context, {
    this.title,
    this.content,
    this.complete,
    this.cancelBtnTitle,
    this.sureBtnTitle,
    this.sureBtnTitleStyle,
    this.showCancelBtn = true,
  });

  void show() {
    showDialog(
      context: context,
      builder: (context) {
        return Material(
          type: MaterialType.transparency,
          child: Center(
            child: Container(
              width: 300.gw,
              padding: EdgeInsets.only(top: 17.gw),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Theme.of(context).scaffoldBackgroundColor,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (title != null) Text(title!, style: context.textTheme.active.fs18),
                  if (content != null)
                    Padding(
                      padding: EdgeInsets.only(top: title != null ? 20.gw : 0, left: 20.gw, right: 20.gw),
                      child: Text(
                        content!,
                        style: context.textTheme.regular,
                      ),
                    ),
                  SizedBox(height: 30.gw),
                  Divider(height: 1, color: Theme.of(context).dividerColor),
                  SizedBox(
                    height: 45.gw,
                    child: Row(
                      children: [
                        if (showCancelBtn) ...[
                          Expanded(
                            child: _buildBtn(
                              context,
                              title: cancelBtnTitle ?? "cancel".tr(),
                            ),
                          ),
                          Container(
                            width: 1.gw,
                            height: double.infinity,
                            color: Theme.of(context).dividerColor,
                          ),
                        ],
                        Expanded(
                          child: _buildBtn(
                            context,
                            title: sureBtnTitle ?? "confirm".tr(),
                            titleStyle: sureBtnTitleStyle ?? context.textTheme.primary,
                            onTap: () {
                              if (complete != null) {
                                complete!();
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBtn(BuildContext context, {required String title, titleStyle, GestureTapCallback? onTap}) {
    return InkWell(
      onTap: () {
        HapticFeedback.lightImpact();
        Navigator.of(context).pop();
        onTap?.call();
      },
      child: Center(
        child: Text(
          title,
          style: titleStyle ?? context.textTheme.title,
        ),
      ),
    );
  }
}
