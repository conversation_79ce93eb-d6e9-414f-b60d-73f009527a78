import 'package:flutter/services.dart';
/// 正整数且首位不为0
class NoLeadingZeroIntFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue,
      TextEditingValue newValue,
      ) {
    final t = newValue.text;

    // 允许空串（便于删除）
    if (t.isEmpty) return newValue;

    // 仅允许数字
    if (!RegExp(r'^\d+$').hasMatch(t)) return oldValue;

    // 不允许前导0
    if (t.startsWith('0')) return oldValue;

    return newValue;
  }
}
