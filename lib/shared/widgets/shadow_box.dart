import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class ShadowBox extends StatelessWidget {
  const ShadowBox({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius,
  });

  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final BorderRadius? borderRadius;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      padding: padding ?? EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: borderRadius ?? BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: context.theme.primaryColor.withNewOpacity(0.08),
            blurRadius: 8.gw, // Soft blur effect
            spreadRadius: 0,
            offset: Offset(0, 4.gw), // Downward shadow
          ),
        ],
      ),
      child: child,
    );
  }
}
