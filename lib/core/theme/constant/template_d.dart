import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/theme/custom_theme_color.dart';

class CustomColorThemeConstantTemplateD {
  /// 亮色主题配色 Light mode color palette
  static const CustomColorTheme lightDefault = CustomColorTheme(
    textPrimary: Color(0xFF269AFF),
    textTitle: Color(0xFF525A79),
    textRegular: Color(0xFFAFB8CB),
    textSecondary: Color(0xFFFFFFFF),
    textTertiary: Color(0x00000000),
    textHighlight: Color(0xFF269AFF),
    tabActive: Colors.black,
    tabInactive: Color(0xFFBDBDBD),
    tabInactiveBg: Color(0xFFF1F4FF),
    buttonPrimary: Colors.white,
    buttonSecondary: Color(0xFF000000),
    stockRed: Color(0xFFC92C31),
    stockGreen: Color(0xFF1CB570),
    border: Color(0xFFAFB8CB),
    pending: Color(0xFFF8BB18),
    inviteBackgroundStart: Color(0xFF269AFF),
    inviteBackgroundEnd: Color(0xffF7F8F8),
  );

  /// 暗黑主题配色 Dark mode color palette
  static const CustomColorTheme darkDefault = CustomColorTheme(
    textPrimary: Color(0xFF525A79),
    textTitle: Color(0xFF525A79),
    textRegular: Color(0xFFAFB8CB),
    textSecondary: Color(0xFFFFFFFF),
    textTertiary: Color(0x00000000),
    textHighlight: Color(0xFF269AFF),
    tabActive: Colors.white,
    tabInactive: Color(0xFF666666),
    tabInactiveBg: Color(0xFFF1F4FF),
    buttonPrimary: Color(0xFF1F1911),
    buttonSecondary: Colors.white,
    stockRed: Color(0xFFC92C31),
    stockGreen: Color(0xFF1CB570),
    border: Color(0xFFAFB8CB),
    pending: Color(0xFFF8BB18),
    inviteBackgroundStart: Color(0xFF269AFF),
    inviteBackgroundEnd: Color(0xffF7F8F8),
  );
}

class ThemeConstantTemplateD {
  ThemeConstantTemplateD._();

  static final ThemeConstantTemplateD instance = ThemeConstantTemplateD._();

  ThemeData get lightDefault => ThemeData.light().copyWith(
        // 主题色 / Primary theme color
        primaryColor: const Color(0xFF269AFF),

        // 主题色-浅色 / Light variant of primary color
        primaryColorLight: const Color(0xFF1890FF),

        // Scaffold的默认背景颜色 / Default background color of Scaffold
        scaffoldBackgroundColor: const Color(0xffF4F7FE),

        // 用于提示文本或占位符文本的颜色 / Color for hint or placeholder text
        hintColor: const Color(0xFFAFB8CB),

        // 点击时的高亮效果 / Highlight color on tap
        splashColor: Colors.transparent,

        // 长按时的扩散效果 / Ripple color on long press
        highlightColor: Colors.transparent,

        // 用于未选中的复选框 / Color for unselected checkbox
        unselectedWidgetColor: const Color(0xffE9EDF3),

        // 禁用状态下部件的颜色 / Color for disabled components
        disabledColor: const Color(0xff9BA6B6),

        // 鼠标悬停时的颜色 / Hover color when mouse hovers
        hoverColor: const Color(0xffE7E7E7),

        // 阴影颜色 / Shadow color
        shadowColor: const Color(0x14354677),

        // 焦点状态下的颜色 / Focus color
        focusColor: const Color(0xffFFF2E8),

        // 分割线 / Divider line color
        dividerColor: const Color(0xFF0F0F0F).withValues(alpha: 0.1),

        appBarTheme: const AppBarTheme(
          // AppBar背景色 / AppBar background color
          backgroundColor: Color(0xFFF4F7FE),

          titleTextStyle: TextStyle(
            color: Color(0xFF000000),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),

          // AppBar图标颜色 / Icon color in AppBar
          iconTheme: IconThemeData(color: Color(0xFF525A79)),
        ),

        bottomNavigationBarTheme: const BottomNavigationBarThemeData(
          // BottomNavigation背景 / Background of bottom navigation
          backgroundColor: Color(0xFFFFFFFF),
          // 选中项颜色 / Selected item color
          selectedItemColor: Color(0xFF269AFF),
          // 未选中项颜色 / Unselected item color
          unselectedItemColor: Color(0xFFAFB8CB),
        ),

        tabBarTheme: TabBarTheme(
          // 取消水波纹 / Disable ripple
          splashFactory: NoSplash.splashFactory,
          // 点击阴影为透明 / Transparent overlay on tap
          overlayColor: WidgetStateProperty.all(Colors.transparent),
          // 分隔线颜色 / Divider color
          dividerColor: Colors.transparent,
        ),

        // 卡片背景色 / Card background color
        cardColor: Color(0xFFFFFFFF),

        cardTheme: const CardTheme(
          // 卡片背景色 / Card background color
          color: Color(0xFFFFFFFF),
        ),

        // icon 背景色
        iconButtonTheme: IconButtonThemeData(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Colors.transparent),
            minimumSize: WidgetStateProperty.all(Size(40, 40)),
            shape: WidgetStateProperty.all(CircleBorder()),
          ),
        ),

        buttonTheme: const ButtonThemeData(
          // 点击时的高亮效果 / Highlight on tap
          splashColor: Colors.transparent,
          // 长按时的扩散效果 / Ripple on long press
          highlightColor: Colors.transparent,
        ),

        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ButtonStyle(
            // 去除 ElevatedButton 阴影 / Remove elevation
            elevation: WidgetStateProperty.all(0),
          ),
        ),

        inputDecorationTheme: InputDecorationTheme(
          filled: true,

          // TextField 背景色 / Background color of input field
          fillColor: Color(0xFFF2F5FF),

          hintStyle: TextStyle(
            // 设置 hintText 的颜色 / Hint text color
            color: Color(0xFFAFB8CB),
          ),

          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: Color(0xFF269AFF),
            ),
          ),
        ),

        extensions: <ThemeExtension<dynamic>>[
          CustomColorThemeConstantTemplateD.lightDefault,
          CustomTextTheme.light,
        ],
      );

  ThemeData get darkDefault => ThemeData.dark().copyWith(
        // 主题色 / Primary theme color
        primaryColor: const Color(0xFF269AFF),

        // 主题色-浅色 / Light variant of primary color
        primaryColorLight: const Color(0xFF1890FF),

        // Scaffold的默认背景颜色 / Default background color of Scaffold
        scaffoldBackgroundColor: const Color(0xff04040B),

        // 用于提示文本或占位符文本的颜色 / Color for hint or placeholder text
        hintColor: const Color(0xFF269AFF),

        // 点击时的高亮效果 / Highlight color on tap
        splashColor: Colors.transparent,

        // 长按时的扩散效果 / Ripple color on long press
        highlightColor: Colors.transparent,

        // 用于未选中的复选框 / Color for unselected checkbox
        unselectedWidgetColor: const Color(0xffE9EDF3),

        // 禁用状态下部件的颜色 / Color for disabled components
        disabledColor: const Color(0xff9BA6B6),

        // 鼠标悬停时的颜色 / Hover color when mouse hovers
        hoverColor: const Color(0xffE7E7E7),

        // 阴影颜色 / Shadow color
        shadowColor: const Color(0x14354677),

        // 焦点状态下的颜色 / Focus color
        focusColor: const Color(0xffFFF2E8),

        // 分割线 / Divider line color
        dividerColor: const Color(0xFF868989),

        appBarTheme: AppBarTheme(
          // AppBar背景色 / AppBar background color
          backgroundColor: Color(0xff160D00),

          titleTextStyle: TextStyle(
            color: Color(0xFFFFFFFF),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),

          // AppBar图标颜色 / Icon color in AppBar
          iconTheme: IconThemeData(color: Color(0xFF525A79)),
        ),

        bottomNavigationBarTheme: const BottomNavigationBarThemeData(
          // BottomNavigation背景 / Background of bottom navigation
          backgroundColor: Color(0xff0F1233),
          // 选中项颜色 / Selected item color
          selectedItemColor: Color(0xFF269AFF),
          // 未选中项颜色 / Unselected item color
          unselectedItemColor: Color(0xFF65779E),
        ),

        tabBarTheme: TabBarTheme(
          // 取消水波纹 / Disable ripple
          splashFactory: NoSplash.splashFactory,
          // 点击阴影为透明 / Transparent overlay on tap
          overlayColor: WidgetStateProperty.all(Colors.transparent),
          // 分隔线颜色 / Divider color
          dividerColor: Colors.transparent,
        ),

        // 卡片背景色 / Card background color
        cardColor: Color(0xFF0F1233),

        cardTheme: const CardTheme(
          // 卡片背景色 / Card background color
          color: Color(0xFF0F1233),
        ),

        // icon 背景色
        iconButtonTheme: IconButtonThemeData(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Colors.transparent),
            minimumSize: WidgetStateProperty.all(Size(40, 40)),
            shape: WidgetStateProperty.all(CircleBorder()),
          ),
        ),

        buttonTheme: const ButtonThemeData(
          // 点击时的高亮效果 / Highlight on tap
          splashColor: Colors.transparent,
          // 长按时的扩散效果 / Ripple on long press
          highlightColor: Colors.transparent,
        ),

        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ButtonStyle(
            // 去除 ElevatedButton 阴影 / Remove elevation
            elevation: WidgetStateProperty.all(0),
          ),
        ),

        inputDecorationTheme: InputDecorationTheme(
          filled: true,

          // TextField 背景色 / Background color of input field
          fillColor: Color(0xFF192440),

          hintStyle: TextStyle(
            // 设置 hintText 的颜色 / Hint text color
            color: Color(0xFF65779E),
          ),

          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: Color(0xFF269AFF),
            ),
          ),
        ),

        extensions: <ThemeExtension<dynamic>>[
          CustomColorThemeConstantTemplateD.darkDefault,
          CustomTextTheme.dart,
        ],
      );
}
