import 'package:easy_localization/easy_localization.dart';

class StringUtil {
  /// 是否是空
  static bool isEmpty(dynamic res) {
    if (res == null) return true;

    if (res is String || res is Iterable || res is Map) {
      return res.isEmpty;
    }

    return false;
  }

  /// 补全url
  static String fixUrl(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) return 'https://$url'; // 解析失败也补全
    if (uri.hasScheme) return url;
    return 'https://$url';
  }

  /// Mask mobile for confidentiality
  /// eg: 13812345678 -> 138****5678
  static String maskMobile(String? mobile) {
    if (mobile == null || mobile.length < 7) return mobile ?? '';
    return '${mobile.substring(0, 3)}****${mobile.substring(mobile.length - 4)}';
  }
}

extension MoneyExtension on num {
  /// 返回格式化的金额字符串
  String get formattedMoney {
    final number = _removeInvalidZeros(this);
    return _formatWithThousandsSeparator(number);
  }

  String get removeZeros {
    return _removeInvalidZeros(this);
  }

  /// 删除多余的0
  String _removeInvalidZeros(num value) {
    // 保留两位小数，并移除末尾多余的0
    return value.toStringAsFixed(2).replaceAll(RegExp(r'([.]*0+)(?!.*\d)'), '');
  }

  /// 格式化为千分位
  String _formatWithThousandsSeparator(String value) {
    final numberFormat = NumberFormat('#,##0', 'en_US');

    numberFormat.minimumFractionDigits = 0;
    numberFormat.maximumFractionDigits = 2;

    return numberFormat.format(double.parse(value));
  }
}