class StringUtil {
  /// 是否是空
  static bool isEmpty(dynamic res) {
    if (res == null) return true;

    if (res is String || res is Iterable || res is Map) {
      return res.isEmpty;
    }

    return false;
  }

  /// 补全url
  static String fixUrl(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) return 'https://$url'; // 解析失败也补全
    if (uri.hasScheme) return url;
    return 'https://$url';
  }

  /// Mask mobile for confidentiality
  /// eg: 13812345678 -> 138****5678
  static String maskMobile(String? mobile) {
    if (mobile == null || mobile.length < 7) return mobile ?? '';
    return '${mobile.substring(0, 3)}****${mobile.substring(mobile.length - 4)}';
  }
}
