import 'dart:math';

import 'package:decimal/decimal.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/features/account/domain/models/calculate_config/calculate_config.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';

import '../../features/account/domain/models/account_summary/contract_summary_response.dart';
import '../../shared/models/stock/stock_response.dart';

String? getInstrumentId(StockItem? data) {
  if (data == null || data.market == null || data.securityType == null || data.symbol == null) {
    return null;
  }
  return '${data.market}|${data.securityType}|${data.symbol}';
}

String getInstrumentIdFromMarketType(String marketType) {
  final market = MarketSymbol.fromMarketType(marketType);
  return '${market.market}|${market.securityType}|${market.symbol}';
}

Instrument getTradingArguments(String marketType) {
  final instrument = getInstrumentIdFromMarketType(marketType);
  return Instrument(instrument: instrument);
}

MainMarketType getMainMarketType(String marketType) {
  return switch (marketType) {
    'CN' || 'SZSE' => MainMarketType.cnShares,
    'HK' || 'HKEX' => MainMarketType.hkShares,
    'US' => MainMarketType.usShares,
    _ => MainMarketType.cnShares,
  };
}

MainMarketType getMarketType(StockItem data) {
  return switch (data.market) {
    'SZSE' => MainMarketType.cnShares,
    'HKEX' => MainMarketType.hkShares,
    'US' => MainMarketType.usShares,
    _ => MainMarketType.cnShares,
  };
}

String formatNumberWithChineseUnits(double number) {
  if (number >= 100000000) {
    double value = number / 100000000;
    return '${value % 1 == 0 ? value.toStringAsFixed(0) : value.toStringAsFixed(0)}亿';
  } else if (number >= 1000000) {
    double value = number / 10000;
    return '${value % 1 == 0 ? value.toStringAsFixed(0) : value.toStringAsFixed(0)}万';
  } else if (number >= 10000) {
    double value = number / 10000;
    return '${value % 1 == 0 ? value.toStringAsFixed(0) : value.toStringAsFixed(0)}万';
  }
  return number.toStringAsFixed(0);
}

String getMarketTypeLabel(String marketType) => switch (marketType) {
      'CN' => MarketSymbol.CN.displayName,
      'HK' => MarketSymbol.HK.displayName,
      'US' => MarketSymbol.US.displayName,
      _ => MarketSymbol.CN.displayName,
    };

// example: {en-US: 英, zh-HK: 繁, zh-CN: 中}
String getLocalizedText(Map<String, dynamic>? textMap, BuildContext context) {
  if (textMap == null) return '';

  final locale = "${context.locale.languageCode}-${context.locale.countryCode}";
  final key = (locale == 'zh-CN' || locale == 'zh-HK') ? 'zh-CN' : 'en-US';
  return textMap[key] ?? textMap['en-US'] ?? '';
}

double calculateIndexHandleFee({
  required num tradeAmount,
  required List<CalculateConfig> chargeList,
  required int tradeUnit,
  bool enableLog = false,
}) {
  Decimal fee0 = Decimal.zero;

  for (int i = 0; i < chargeList.length; i++) {
    var charge = chargeList[i];

    final tradeAmountDecimal = Decimal.parse(tradeAmount.toString());
    final calculateValueDecimal = Decimal.parse(charge.calculateValue.toString());
    fee0 =
        tradeAmountDecimal * ((calculateValueDecimal / Decimal.fromInt(100)).toDecimal()) * Decimal.fromInt(tradeUnit);

    Function? roundFn;
    switch (charge.roundType) {
      // 进一
      case 1:
        roundFn = (Decimal value, int precision) => value.ceil(scale: precision).toDouble();
        if (enableLog) debugPrint('Round type 1 (ceiling)');
        break;
      // 四舍五入
      case 2:
        roundFn = (Decimal value, int precision) => value.round(scale: precision).toDouble();
        if (enableLog) debugPrint('Round type 2 (round)');
        break;
      // 去尾
      case 3:
        roundFn = (Decimal value, int precision) => value.truncate(scale: precision).toDouble();
        if (enableLog) debugPrint('Round type 3 (truncate)');
        break;
    }

    double tempFee;
    if (roundFn != null) {
      double roundedValue = roundFn(fee0, charge.roundPrecision);

      tempFee = clampDouble(roundedValue, charge.min, charge.max);
    } else {
      tempFee = fee0.toDouble();
    }
    fee0 = Decimal.parse(tempFee.toString());
  }
  return fee0.toDouble();
}

({double buyFee, double sellFee}) calculateHandleFee({
  required num tradeAmount,
  required double tradeNum,
  required List<CalculateConfig> chargeList,
  bool enableLog = false,
}) {
  double buyFee = 0;
  double sellFee = 0;

  for (int i = 0; i < chargeList.length; i++) {
    var charge = chargeList[i];

    Decimal fee0 = Decimal.zero;
    final tradeAmountDecimal = Decimal.parse(tradeAmount.toString());
    final calculateValueDecimal = Decimal.parse(charge.calculateValue.toString());

    switch (charge.calculateType) {
      // % × 交易金额
      case 1:
        final x = calculateValueDecimal / Decimal.fromInt(100);
        fee0 = (tradeAmountDecimal * x.toDecimal());

        // : Added this rounding to match H5 calculation
        fee0 = fee0.round(scale: 2);

        break;
      // 数量
      case 2:
        fee0 = calculateValueDecimal * tradeAmountDecimal;

        break;
      // 每笔
      case 3:
        fee0 = calculateValueDecimal;

        break;
    }

    if (charge.calculateType != 3) {
      fee0 = fee0.clamp(Decimal.parse(charge.min.toString()), Decimal.parse(charge.max.toString()));
    }

    Function? roundFn;
    switch (charge.roundType) {
      // 进一
      case 1:
        roundFn = (Decimal value, int precision) => value.ceil(scale: precision).toDouble();
        break;
      // 四舍五入
      case 2:
        roundFn = (Decimal value, int precision) => value.round(scale: precision).toDouble();
        break;
      // 去尾
      case 3:
        roundFn = (Decimal value, int precision) => value.truncate(scale: precision).toDouble();
        break;
    }

    double fee;
    if (roundFn != null) {
      double roundedValue = roundFn(fee0, charge.roundPrecision);

      fee = clampDouble(roundedValue, charge.min, charge.max);
    } else {
      fee = fee0.toDouble();
    }

    switch (charge.direction) {
      // 买
      case 1:
        buyFee += fee;
        break;
      // 卖
      case 2:
        sellFee += fee;
        break;
      // 买卖
      case 3:
        buyFee += fee;
        sellFee += fee;
        break;
    }
  }

  return (buyFee: buyFee, sellFee: sellFee);
}

// Helper function for clamping double values
double clampDouble(double value, double min, double max) {
  if (value < min) return min;
  if (value > max) return max;
  return value;
}

class TimeFormat {
  static const String full = 'yyyy-MM-dd HH:mm:ss';
  static const String ymd = 'yyyy-MM-dd';
  static const String hm = 'HH:mm';
  static const String md = 'MM-dd';
  static const String ym = 'yyyy-MM';
  static const String y = 'yyyy';
}

Decimal clampDecimal(Decimal value, Decimal min, Decimal max) {
  if (value < min) return min;
  if (value > max) return max;
  return value;
}

String getPeriodLabel(int periodType) => 'contract.period_$periodType'.tr();

String getContractLabel(ContractSummaryData? record) {
  if (record == null) return '';
  final contractType = switch (record.type) {
    1 => ContractType.standard,
    2 => ContractType.experience,
    3 => ContractType.bonus,
    _ => ContractType.standard,
  };

  final contractTypeName = contractType.translationKey.tr();
  final periodName = getPeriodLabel(record.periodType ?? 0);

  final multipleName = '${record.multiple}${'times'.tr()}';

  final marketName = record.marketType ?? '';
  final market = contractMarketTranslation[marketName]?.tr() ?? '';

  return '($contractTypeName)$periodName$multipleName$market[${record.id}]';
}

String getContractLabelByContractSummaryPageRecord(ContractSummaryPageRecord? record) {
  if (record == null) return '';
  final contractType = switch (record.type) {
    1 => ContractType.standard,
    2 => ContractType.experience,
    3 => ContractType.bonus,
    _ => ContractType.standard,
  };

  final contractTypeName = contractType.translationKey.tr();
  final periodName = getPeriodLabel(record.periodType);

  final multipleName = '${record.multiple}${'times'.tr()}';

  final marketName = record.marketType;
  final market = contractMarketTranslation[marketName]?.tr() ?? '';

  return '($contractTypeName)$periodName$multipleName$market[${record.id}]';
}

String formatLargeNumber(double amount, String locale, {int precision = 2}) {
  double value;
  String unit = '';

  if (locale.startsWith('zh')) {
    if (amount < 10000) {
      value = amount;
    } else if (amount < 1000000) {
      value = amount / 10000;
      unit = '万';
    } else if (amount < 10000000) {
      value = amount / 1000000;
      unit = '百万';
    } else if (amount < 100000000) {
      value = amount / 10000000;
      unit = '千万';
    } else if (amount < 100000000000) {
      value = amount / 100000000;
      unit = '亿';
    } else {
      value = amount / 100000000000;
      unit = '千亿';
    }
  } else {
    if (amount < 1000) {
      value = amount;
    } else if (amount < 1000000) {
      value = amount / 1000;
      unit = 'K';
    } else if (amount < 1000000000) {
      value = amount / 1000000;
      unit = 'M';
    } else if (amount < 1000000000000) {
      value = amount / 1000000000;
      unit = 'B';
    } else if (amount < 1000000000000000) {
      value = amount / 1000000000000;
      unit = 'T';
    } else {
      value = amount / 1000000000000000;
      unit = 'Q';
    }
  }

  return '${value.toStringAsFixed(precision)}$unit';
}

bool isDarkMode(BuildContext context) {
  return Theme.of(context).brightness == Brightness.dark;
}

///是否是空
bool isEmpty(dynamic res) {
  if (res == null) return true;

  if (res is String || res is Iterable || res is Map) {
    return res.isEmpty;
  }

  return false;
}

/// 随机生成字符串
String generateRandomString(int length) {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  final random = Random();
  return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
}
