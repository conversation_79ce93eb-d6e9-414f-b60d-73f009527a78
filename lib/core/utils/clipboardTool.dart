import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';

//复制工具
class ClipboardTool {
  static void setData(String? data) {
    return setDataToast(data, msg: 'copied'.tr());
  }

  //复制内容
  static void setDataToast(String? data, {String? msg}) {
    if (data != null) {
      Clipboard.setData(ClipboardData(text: data));
      if (msg != null) {
        GPEasyLoading.showToast(msg.tr(), gravity: ToastGravity.CENTER);
      }
    }
  }

  //获取内容
  static Future<ClipboardData?> getData() {
    return Clipboard.getData(Clipboard.kTextPlain);
  }
}
