import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'dart:convert';

import 'package:gp_stock_app/generated/json/usdt_channel.g.dart';

@JsonSerializable()
class USDTChannelListEntity {
  List<USDTChannel> list = [];

  USDTChannelListEntity();

  factory USDTChannelListEntity.fromJson(Map<String, dynamic> json) => $USDTChannelListEntityFromJson(json);

  Map<String, dynamic> toJson() => $USDTChannelListEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class USDTChannel {
  String currency = ''; // 币种，例如 USDT

  double exchangeRate = 0.0; // 充值汇率

  int giveGiftType = 0; // 赠送赠品类型，1=现金，2=利息券

  double giveRate = 0.0; // 赠送的数值

  int giveRuleType = 0; // 赠送规则类型，1=固定金额，2=按充值金额比例

  int id = 0; // 唯一标识

  double maxAmount = 0.0; // 最大充值金额

  double minAmount = 0.0; // 最小充值金额

  int network = 0; // 网络类型，1=TRC20, 2=ERC20

  @JSONField(name: "netWorkName")
  String networkName = ''; // 网络名称 TRC20 ERC20

  String rechargeAddress = ''; // 充值地址

  String rechargeAmountOptions = ''; // 可选充值金额配置，逗号分隔

  List<String> rechargeAmountOptionsList = const []; // 可选充值金额数组

  USDTChannel();

  factory USDTChannel.fromJson(Map<String, dynamic> json) => $USDTChannelFromJson(json);

  Map<String, dynamic> toJson() => $USDTChannelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class UsdtRechargeOrder {
  /// 汇率
  double exchangeRate = 0;

  /// id，唯一标识
  int id = 0;

  /// 充值网络 1=TRC20, 2=ERC20
  int network = 0;

  /// 网络名称 TRC20, ERC20
  @JSONField(name: "netWorkName")
  String networkName = '';

  /// 订单金额
  double orderAmount = 0;

  /// 订单号
  String orderNo = '';

  /// 订单最初金额（没有换算汇率前）
  double orderOriginAmount = 0;

  /// 充值地址
  String rechargeAddress = '';

  /// USDT 金额
  double usdtAmount = 0;

  UsdtRechargeOrder();

  factory UsdtRechargeOrder.fromJson(Map<String, dynamic> json) => $UsdtRechargeOrderFromJson(json);

  Map<String, dynamic> toJson() => $UsdtRechargeOrderToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
