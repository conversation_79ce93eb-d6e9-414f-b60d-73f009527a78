import 'dart:convert';

import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/withdraw_channel_entity.g.dart';

export 'package:gp_stock_app/generated/json/withdraw_channel_entity.g.dart';

@JsonSerializable()
class WithdrawChannelListEntity {
  List<WithdrawChannel> list = [];

  WithdrawChannelListEntity();

  factory WithdrawChannelListEntity.fromJson(Map<String, dynamic> json) => $WithdrawChannelListEntityFromJson(json);

  Map<String, dynamic> toJson() => $WithdrawChannelListEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class WithdrawChannel {
  String currency = "";
  double exchangeRate = 0.0;
  int handlingFeeType = 0;
  int id = 0;
  int maxWithdrawalAmount = 0;
  int minWithdrawalAmount = 0;
  String netWorkName = "";
  int network = 0;
  String withdrawalAmountOptions = "";
  List<String> withdrawalAmountOptionsList = [];
  int withdrawalFee = 0;

  WithdrawChannel();

  factory WithdrawChannel.fromJson(Map<String, dynamic> json) => $WithdrawChannelFromJson(json);

  Map<String, dynamic> toJson() => $WithdrawChannelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
