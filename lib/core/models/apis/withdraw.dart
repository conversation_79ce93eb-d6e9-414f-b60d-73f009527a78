

import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/deposit/usdt_channel.dart';
import 'package:gp_stock_app/core/models/entities/task/task_center_response_entity.dart';
import 'package:gp_stock_app/core/models/entities/withdraw_channel_entity.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

/// 提现
class WithdrawApi {
  /// 获取所有USDT提现渠道
    static Future<List<WithdrawChannel>> fetchUsdtWithdrawList() async {
      final res = await Http().request<WithdrawChannelListEntity>(
        ApiEndpoints.getWithdrawUsdtList,
        method: HttpMethod.get,
      );
      return res.data?.list ?? [];
    }



  /// USDT提现
  static Future<bool> applyUSDTWithdraw({
    required double amount, // 提现金额
    required int id, // 钱包地址ID
    required String password, // 资金密码
    required int walletId, // 提现通道ID
  }) async {
    final res = await Http().request<bool>(
      ApiEndpoints.withdrawUSDT,
      method: HttpMethod.post,
      params: {
        "amount": amount,
        "id": id,
        "password": password,
        "walletId": walletId,
      },
    );
    return res.isSuccess;
  }
}