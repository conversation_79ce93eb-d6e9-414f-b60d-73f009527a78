import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/string_util.dart';
import 'package:gp_stock_app/features/invite/logic/rebate/rebate_cubit.dart';
import 'package:gp_stock_app/features/invite/domain/models/rebate_detail_model.dart';
import 'package:gp_stock_app/shared/widgets/error/error_retry_widget.dart';
import 'package:gp_stock_app/shared/widgets/pagination/common_refresher.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class RebateDetailsScreen extends StatefulWidget {
  final AppSkinStyle? skinStyle;

  const RebateDetailsScreen({super.key, this.skinStyle});

  @override
  State<RebateDetailsScreen> createState() => _RebateDetailsScreenState();
}

class _RebateDetailsScreenState extends State<RebateDetailsScreen> {
  final TextEditingController _searchController = TextEditingController();
  final RefreshController _refreshController = RefreshController();
  List<RebateDetailModel> _originalList = [];
  late RebateCubit _rebateCubit;

  @override
  void initState() {
    super.initState();
    _rebateCubit = RebateCubit();
    _searchController.addListener(_onSearchChanged);
    _loadRecords();
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _refreshController.dispose();
    _rebateCubit.close();
    super.dispose();
  }

  void _loadRecords() {
    _rebateCubit.getRebateDetails();
  }

  void _onRefresh() async {
    await _rebateCubit.getRebateDetails();
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    final state = _rebateCubit.state;
    if ((state.rebateDetails.length) >= (state.totalPages * 20)) {
      _refreshController.loadNoData();
    } else {
      await _rebateCubit.getRebateDetails(loadMore: true);
      _refreshController.loadComplete();
    }
  }

  void _onSearchChanged() {
    final query = _searchController.text.trim();
    setState(() {});
    if (query.isEmpty) {
      _rebateCubit.getRebateDetails();
    }
  }

  void _onSearchPressed() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty) {
      _rebateCubit.searchRebateDetails(query);
    } else {
      _rebateCubit.getRebateDetails();
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentSkinStyle = widget.skinStyle ?? AppConfig.instance.skinStyle;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(context, currentSkinStyle),
      body: BlocProvider.value(
        value: _rebateCubit,
        child: BlocBuilder<RebateCubit, RebateState>(
          builder: (context, state) {
            if (state.getRebateDetailsStatus == DataStatus.loading &&
                state.rebateDetails.isEmpty &&
                !state.isLoadMore) {
              return const Center(child: CircularProgressIndicator());
            }

            if (state.getRebateDetailsStatus == DataStatus.failed && state.rebateDetails.isEmpty) {
              return ErrorRetryWidget(
                errorMessage: state.error,
                onRetry: () => _rebateCubit.refresh(),
              );
            }

            _originalList = state.rebateDetails;

            return _buildBody(context, currentSkinStyle);
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context, AppSkinStyle skinStyle) {
    return AppBar(
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios,
          color: context.colorTheme.textTitle,
          size: 20.gw,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'rebateDetails'.tr(),
        style: context.textTheme.title.fs18.w600,
      ),
      backgroundColor: Colors.white,
      elevation: 0,
    );
  }

  Widget _buildBody(BuildContext context, AppSkinStyle skinStyle) {
    return Column(
      children: [
        _buildSearchBar(context),
        Divider(height: 1, color: Colors.grey[200]),
        _buildHeader(context),
        Expanded(
          child: _buildRebateDetailsList(context),
        ),
      ],
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(8.gr),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 40.gh,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8.gr),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: TextField(
                controller: _searchController,
                style: context.textTheme.primary.copyWith(fontSize: 14.gsp),
                decoration: InputDecoration(
                  hintText: 'searchByNameOrPhone'.tr(),
                  hintStyle: context.textTheme.tertiary.copyWith(fontSize: 14.gsp),
                  prefixIcon: Icon(
                    Icons.search,
                    color: context.colorTheme.textTertiary,
                    size: 20.gr,
                  ),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: Icon(
                            Icons.clear,
                            color: context.colorTheme.textTertiary,
                            size: 20.gr,
                          ),
                          onPressed: () {
                            _searchController.clear();
                          },
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 8.gh),
                ),
                onSubmitted: (_) => _onSearchPressed(),
              ),
            ),
          ),
          SizedBox(width: 10.gw),
          InkWell(
            onTap: _onSearchPressed,
            child: Container(
              height: 40.gh,
              padding: EdgeInsets.symmetric(horizontal: 16.gw),
              decoration: BoxDecoration(
                color: context.colorTheme.stockRed,
                borderRadius: BorderRadius.circular(8.gr),
              ),
              child: Center(
                child: Text(
                  'search'.tr(),
                  style: context.textTheme.secondary.copyWith(fontSize: 14.gsp, fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 16.gh),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              'memberName'.tr(),
              style: context.textTheme.tertiary.copyWith(fontSize: 12.gsp, fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'refundAmount'.tr(),
              style: context.textTheme.tertiary.copyWith(fontSize: 12.gsp, fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'rebateAmount'.tr(),
              style: context.textTheme.tertiary.copyWith(fontSize: 12.gsp, fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              'more'.tr(),
              style: context.textTheme.tertiary.copyWith(fontSize: 12.gsp, fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRebateDetailsList(BuildContext context) {
    return BlocBuilder<RebateCubit, RebateState>(
      builder: (context, state) {
        if (state.getRebateDetailsStatus == DataStatus.loading && state.rebateDetails.isEmpty) {
          return const Center(child: CircularProgressIndicator.adaptive());
        }

        if (state.getRebateDetailsStatus == DataStatus.failed) {
          return Center(child: Text(state.error ?? 'failedToLoad'.tr()));
        }

        if (state.rebateDetails.isEmpty) {
          return CommonRefresher(
            controller: _refreshController,
            enablePullDown: true,
            onRefresh: _onRefresh,
            child: ListView(
              physics: const AlwaysScrollableScrollPhysics(),
              children: [
                SizedBox(
                  height: 0.7.gsh,
                  child: Center(
                    child: Text('noRebateDetailsFound'.tr(),
                        style: context.textTheme.regular.copyWith(
                          color: context.colorTheme.textRegular,
                        )),
                  ),
                ),
              ],
            ),
          );
        }

        return CommonRefresher(
          controller: _refreshController,
          enablePullDown: true,
          enablePullUp: true,
          onRefresh: _onRefresh,
          onLoading: _onLoading,
          child: ListView.builder(
            padding: EdgeInsets.all(16.gr),
            itemCount: state.rebateDetails.length,
            itemBuilder: (context, index) {
              final rebate = state.rebateDetails[index];
              return _buildRebateDetailItem(context, rebate);
            },
          ),
        );
      },
    );
  }

  Widget _buildRebateDetailItem(BuildContext context, RebateDetailModel rebate) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.gh),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              rebate.name ?? '--',
              style: context.textTheme.title,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              rebate.refundAmount?.toStringAsFixed(2) ?? '0.00',
              style: context.textTheme.title.copyWith(color: context.colorTheme.stockRed),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              rebate.rebateAmount?.toStringAsFixed(2) ?? '0.00',
              style: context.textTheme.title,
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 1,
            child: GestureDetector(
              onTap: () => _showRebateDetails(context, rebate.id ?? 0),
              child: Text(
                'details'.tr(),
                style: context.textTheme.title.copyWith(
                  color: context.colorTheme.stockRed,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showRebateDetails(BuildContext context, int rebateId) async {
    if (rebateId == 0) return;

    // Find the rebate detail from the existing list
    final detailInfo = _originalList.firstWhere(
      (rebate) => rebate.id == rebateId,
      orElse: () => const RebateDetailModel(),
    );

    if (!mounted) return;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.gr)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(20.gr),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                'rebateDetailsInfo'.tr(),
                style: context.textTheme.title.copyWith(fontSize: 18.gsp, fontWeight: FontWeight.w600),
              ),
            ),
            SizedBox(height: 20.gh),
            _buildDetailRow(context, 'memberName'.tr(), detailInfo.name ?? '--'),
            _buildDetailRow(context, 'phoneNumber'.tr(), StringUtil.maskMobile(detailInfo.mobile ?? '')),
            _buildDetailRow(context, 'contractName'.tr(), _getContractNameText(detailInfo)),
            _buildDetailRow(context, 'rebateAmount'.tr(), detailInfo.rebateAmount?.toStringAsFixed(4) ?? '0.0000'),
            _buildDetailRow(context, 'refundAmount'.tr(), detailInfo.refundAmount?.toStringAsFixed(4) ?? '0.0000'),
            _buildDetailRow(context, 'contractNote'.tr(), detailInfo.contractNumber ?? '--'),
            _buildDetailRow(context, 'createTime'.tr(), detailInfo.createTime ?? '--'),
            SizedBox(height: 20.gh),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.gh),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: context.textTheme.tertiary,
          ),
          Text(
            value,
            style: context.textTheme.primary,
          ),
        ],
      ),
    );
  }

  String _getContractNameText(RebateDetailModel detailInfo) {
    final contractType = switch (detailInfo.contractType) {
      1 => ContractType.standard,
      2 => ContractType.experience,
      3 => ContractType.bonus,
      _ => ContractType.standard,
    };
    final contractTypeName = contractType.translationKey.tr();
    final periodName = 'contract.period_${detailInfo.periodType ?? 1}'.tr();
    final multipleName = '${detailInfo.multiple ?? 1}${'times'.tr()}';
    final marketName = detailInfo.marketType ?? '';
    final market = contractMarketTranslation[marketName]?.tr() ?? '';

    return '($contractTypeName)$periodName$multipleName$market[${detailInfo.contractId ?? ''}]';
  }
}
