import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/invite/logic/subordinate/subordinate_cubit.dart';
import 'package:gp_stock_app/features/invite/domain/models/subordinate_model.dart';
import 'package:gp_stock_app/shared/widgets/error/error_retry_widget.dart';
import 'package:gp_stock_app/shared/widgets/pagination/common_refresher.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class SubordinateListScreen extends StatefulWidget {
  final AppSkinStyle? skinStyle;

  const SubordinateListScreen({super.key, this.skinStyle});

  @override
  State<SubordinateListScreen> createState() => _SubordinateListScreenState();
}

class _SubordinateListScreenState extends State<SubordinateListScreen> {
  final TextEditingController _searchController = TextEditingController();
  final RefreshController _refreshController = RefreshController();
  late SubordinateCubit _subordinateCubit;

  @override
  void initState() {
    super.initState();
    _subordinateCubit = SubordinateCubit();
    _searchController.addListener(_onSearchChanged);
    _loadRecords();
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _refreshController.dispose();
    _subordinateCubit.close();
    super.dispose();
  }

  void _loadRecords() {
    _subordinateCubit.getSubordinateList();
  }

  void _onRefresh() async {
    await _subordinateCubit.getSubordinateList();
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    final state = _subordinateCubit.state;
    if ((state.subordinates.length) >= (state.totalPages * 20)) {
      _refreshController.loadNoData();
    } else {
      await _subordinateCubit.getSubordinateList(loadMore: true);
      _refreshController.loadComplete();
    }
  }

  void _onSearchChanged() {
    final query = _searchController.text.trim();
    setState(() {});
    if (query.isEmpty) {
      _subordinateCubit.getSubordinateList();
    }
  }

  void _onSearchPressed() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty) {
      _subordinateCubit.searchSubordinates(query);
    } else {
      _subordinateCubit.getSubordinateList();
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentSkinStyle = widget.skinStyle ?? AppConfig.instance.skinStyle;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(context, currentSkinStyle),
      body: BlocBuilder<SubordinateCubit, SubordinateState>(
        bloc: _subordinateCubit,
        builder: (context, state) {
          if (state.getSubordinateListStatus == DataStatus.failed) {
            return ErrorRetryWidget(
              errorMessage: state.error,
              onRetry: () => _subordinateCubit.refresh(),
            );
          }

          return _buildBody(context, currentSkinStyle);
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context, AppSkinStyle skinStyle) {
    return AppBar(
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios,
          color: context.colorTheme.textTitle,
          size: 20.gw,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'subordinateList'.tr(),
        style: context.textTheme.title.fs18.w600,
      ),
      backgroundColor: Colors.white,
      elevation: 0,
    );
  }

  Widget _buildBody(BuildContext context, AppSkinStyle skinStyle) {
    return Column(
      children: [
        _buildSearchBar(context),
        _buildHeader(context),
        Expanded(
          child: _buildSubordinateList(context),
        ),
      ],
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(8.gr),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 40.gh,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8.gr),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: TextField(
                controller: _searchController,
                style: context.textTheme.primary.copyWith(fontSize: 14.gsp),
                decoration: InputDecoration(
                  hintText: 'searchByNameOrPhone'.tr(),
                  hintStyle: context.textTheme.tertiary.copyWith(fontSize: 14.gsp),
                  prefixIcon: Icon(
                    Icons.search,
                    color: context.colorTheme.textTertiary,
                    size: 20.gr,
                  ),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: Icon(
                            Icons.clear,
                            color: context.colorTheme.textTertiary,
                            size: 20.gr,
                          ),
                          onPressed: () {
                            _searchController.clear();
                          },
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 8.gh),
                ),
                onSubmitted: (_) => _onSearchPressed(),
              ),
            ),
          ),
          SizedBox(width: 10.gw),
          InkWell(
            onTap: _onSearchPressed,
            child: Container(
              height: 40.gh,
              padding: EdgeInsets.symmetric(horizontal: 16.gw),
              decoration: BoxDecoration(
                color: context.colorTheme.stockRed,
                borderRadius: BorderRadius.circular(8.gr),
              ),
              child: Center(
                child: Text(
                  'search'.tr(),
                  style: context.textTheme.secondary.copyWith(fontSize: 14.gsp, fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 12.gh),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              'memberName'.tr(),
              style: context.textTheme.tertiary.copyWith(fontSize: 12.gsp, fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              'cumulativeRebate'.tr(),
              style: context.textTheme.tertiary.copyWith(fontSize: 12.gsp, fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              'memberStatus'.tr(),
              style: context.textTheme.tertiary.copyWith(fontSize: 12.gsp, fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'more'.tr(),
              style: context.textTheme.tertiary.copyWith(fontSize: 12.gsp, fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubordinateList(BuildContext context) {
    return BlocBuilder<SubordinateCubit, SubordinateState>(
      bloc: _subordinateCubit,
      builder: (context, state) {
        if (state.getSubordinateListStatus == DataStatus.loading && state.subordinates.isEmpty) {
          return const Center(child: CircularProgressIndicator.adaptive());
        }

        if (state.getSubordinateListStatus == DataStatus.failed) {
          return Center(child: Text(state.error ?? 'failedToLoad'.tr()));
        }

        if (state.subordinates.isEmpty) {
          return CommonRefresher(
            controller: _refreshController,
            enablePullDown: true,
            onRefresh: _onRefresh,
            child: ListView(
              physics: const AlwaysScrollableScrollPhysics(),
              children: [
                SizedBox(
                  height: 0.7.gsh,
                  child: Center(
                    child: Text('noSubordinatesFound'.tr(),
                        style: context.textTheme.regular.copyWith(
                          color: context.colorTheme.textRegular,
                        )),
                  ),
                ),
              ],
            ),
          );
        }

        return CommonRefresher(
          controller: _refreshController,
          enablePullDown: true,
          enablePullUp: true,
          onRefresh: _onRefresh,
          onLoading: _onLoading,
          child: ListView.builder(
            padding: EdgeInsets.all(16.gr),
            itemCount: state.subordinates.length,
            itemBuilder: (context, index) {
              final subordinate = state.subordinates[index];
              return _buildSubordinateItem(context, subordinate);
            },
          ),
        );
      },
    );
  }

  Widget _buildSubordinateItem(BuildContext context, SubordinateModel subordinate) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.gh),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              subordinate.displayName ?? subordinate.detail?.memberName ?? '--',
              style: context.textTheme.title,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              subordinate.totalRebateFromSubordinate?.toStringAsFixed(2) ?? '0.00',
              style: context.textTheme.title.copyWith(color: context.colorTheme.stockRed),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              _getStatusText(subordinate.status),
              style: context.textTheme.title,
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: GestureDetector(
              onTap: () => _showSubordinateDetails(context, subordinate),
              child: Text(
                'details'.tr(),
                style: context.textTheme.title.copyWith(
                  color: context.colorTheme.stockRed,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusText(bool? status) {
    if (status == true) {
      return 'normal'.tr();
    } else {
      return 'frozen'.tr();
    }
  }

  void _showSubordinateDetails(BuildContext context, SubordinateModel subordinate) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.gr)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(20.gr),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                'subordinateDetails'.tr(),
                style: context.textTheme.primary.fs18.w600,
              ),
            ),
            SizedBox(height: 20.gh),
            _buildDetailRow(context, 'memberName'.tr(), subordinate.detail?.memberName ?? '--'),
            _buildDetailRow(context, 'phoneNumber'.tr(), subordinate.detail?.maskedMobile ?? '--'),
            _buildDetailRow(context, 'idCard'.tr(), subordinate.detail?.maskedIdCard ?? '--'),
            _buildDetailRow(context, 'balance'.tr(), subordinate.detail?.balance?.toStringAsFixed(4) ?? '0.0000'),
            _buildDetailRow(context, 'margin'.tr(), subordinate.detail?.margin?.toStringAsFixed(4) ?? '0.0000'),
            _buildDetailRow(
                context, 'frozenAmount'.tr(), subordinate.detail?.frozenAmount?.toStringAsFixed(4) ?? '0.0000'),
            SizedBox(height: 20.gh),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.gh),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: context.textTheme.tertiary.copyWith(fontSize: 14.gsp),
          ),
          Text(
            value,
            style: context.textTheme.primary.copyWith(fontSize: 14.gsp),
          ),
        ],
      ),
    );
  }
}
