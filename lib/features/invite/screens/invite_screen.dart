import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/string_util.dart';
import 'package:gp_stock_app/features/invite/logic/invite/invite_cubit.dart';
import 'package:gp_stock_app/features/invite/logic/invite/invite_state.dart';
import 'package:gp_stock_app/features/invite/widgets/agent_level_info_cell.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';

import '../../../core/api/network/endpoint/urls.dart';
import '../../../shared/constants/assets.dart';
import '../../../shared/widgets/error/error_retry_widget.dart';
import '../widgets/invite_shimmer_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'subordinate_list_screen.dart';
import 'rebate_details_screen.dart';

class InviteScreen extends StatefulWidget {
  final AppSkinStyle? skinStyle;

  const InviteScreen({super.key, this.skinStyle});

  @override
  State<InviteScreen> createState() => _InviteScreenState();
}

class _InviteScreenState extends State<InviteScreen> {
  @override
  void initState() {
    super.initState();
    context.read<InviteCubit>().getInviteDetails();
  }

  @override
  Widget build(BuildContext context) {
    final currentSkinStyle = widget.skinStyle ?? AppConfig.instance.skinStyle;

    return Scaffold(
      appBar: _buildAppBar(context, currentSkinStyle),
      body: BlocBuilder<InviteCubit, InviteState>(
        builder: (context, state) {
          if (state.status.isLoading) {
            return const InviteScreenShimmer();
          }

          if (state.status.isFailed) {
            return ErrorRetryWidget(
              errorMessage: state.error,
              onRetry: () => context.read<InviteCubit>().getInviteDetails(),
            );
          }

          return _buildBody(context, state, currentSkinStyle);
        },
      ),
    );
  }

  /// Build app bar based on skin style
  PreferredSizeWidget _buildAppBar(BuildContext context, AppSkinStyle skinStyle) {
    switch (skinStyle) {
      case AppSkinStyle.kGP:
        return AppBar(
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: context.colorTheme.textTitle,
              size: 20.gw,
            ),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text('inviteAndEarn'.tr(), style: context.textTheme.title.fs18),
          backgroundColor: context.colorTheme.inviteBackgroundEnd,
          elevation: 0,
        );
      case AppSkinStyle.kTemplateA:
      case AppSkinStyle.kTemplateB:
      case AppSkinStyle.kTemplateC:
      case AppSkinStyle.kTemplateD:
        return AppBar(
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: context.colorTheme.textTitle,
              size: 20.gw,
            ),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text('inviteAndEarn'.tr(), style: context.textTheme.primary.fs18.w600.copyWith(color: Colors.white)),
          backgroundColor: context.colorTheme.inviteBackgroundEnd,
          elevation: 0,
          foregroundColor: Colors.white,
        );
    }
  }

  /// Build body
  Widget _buildBody(BuildContext context, InviteState state, AppSkinStyle skinStyle) {
    return Container(
      decoration: BoxDecoration(
          // color: context.colorTheme.inviteBackgroundEnd,
          ),
      child: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
        child: Column(
          children: [
            // Action buttons
            ColoredBox(
              color: context.colorTheme.inviteBackgroundEnd,
              child: Container(
                margin: EdgeInsets.only(top: 10.gw, left: 10.gw, right: 10.gw, bottom: 0.gw),
                decoration: BoxDecoration(
                  color: context.colorTheme.inviteBackgroundStart,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(26)),
                ),
                padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 16.gw),
                child: Column(
                  children: [
                    _buildActionButtons(context, state),
                    // Stats cards section
                    _buildStatsCards(context, state),
                  ],
                ),
              ),
            ),

            _buildVipLevelTable(context, state),
          ],
        ),
      ),
    );
  }

  /// Build stats cards section
  Widget _buildStatsCards(BuildContext context, InviteState state) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 17.gw, vertical: 12.gw),
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(Assets.inviteStatCardBg),
          fit: BoxFit.cover,
        ),
        borderRadius: BorderRadius.circular(16.gr),
      ),
      child: Column(
        spacing: 10.gw,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// VIP等级
          _buildVipLevelIndicator(context, state),

          /// 累计返佣
          _buildCardItem(context, 'cumulativeRebate'.tr(), state.inviteDetail?.totalRebate?.formattedMoney ?? '0.00'),

          /// 累计下线
          _buildCardItem(context, 'cumulativeSubordinates'.tr(),
              '${state.inviteDetail?.totalSubordinates ?? 0}（${'effective'.tr()}：${state.inviteDetail?.totalSatisfiedInviteCount ?? 0}）'),

          /// 交易返佣比例
          _buildCardItem(context, 'tradingRebateRate'.tr(),
              '${state.inviteDetail?.tradingRebateRate?.formattedMoney ?? '0'}%'),

          /// 利息返佣比例
          _buildCardItem(context, 'interestRebateRate'.tr(),
              '${state.inviteDetail?.interestRebateRate?.formattedMoney ?? '0'}%'),

          /// 累计充值金额
          _buildCardItem(context, 'cumulativeRechargeValue'.tr(), state.inviteDetail?.totalRechargeAmount?.formattedMoney ?? '0.00'),
        ],
      ),
    );
  }

  /// Build VIP level indicator
  Widget _buildVipLevelIndicator(BuildContext context, InviteState state) {
    return Text(
      'VIP${state.inviteDetail?.currentVipLevel ?? 0}',
      style: context.textTheme.primary.fs16.w500.ffAkz.copyWith(color: const Color(0xFFC44500)),
    );
  }

  Widget _buildCardItem(BuildContext context, title, value) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Text(
            title, // 累计返佣
            style: context.textTheme.primary.fs12.copyWith(color: const Color(0xFFC44500)),
          ),
        ),
        SizedBox(width: 10.gw),
        Expanded(
          flex: 6,
          child: Text(
            value,
            style: context.textTheme.primary.fs16.w600.copyWith(color: const Color(0xFF1D2129)),
          ),
        ),
      ],
    );
  }

  /// 代理等级列表
  Widget _buildVipLevelTable(BuildContext context, InviteState state) {
    return Container(
      padding: EdgeInsets.fromLTRB(12.gw, 19.gw, 12.gw, 0),
      color: Color(0xffF4F7FF),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "agentLevelDescription".tr(), // 代理等级说明
            style: context.textTheme.title.fs16.w600,
          ),
          SizedBox(height: 16.gw),
          // Table rows
          ...?state.inviteDetail?.vipLevelTableList?.map((vipLevel) => Padding(
                padding: EdgeInsets.only(bottom: 16.gw),
                child: AgentLevelInfoCell(model: vipLevel),
              )),
        ],
      ),
    );
  }

  /// Build action buttons section
  Widget _buildActionButtons(BuildContext context, InviteState state) {
    return Container(
      padding: EdgeInsets.fromLTRB(8.gw, 0, 8.gw, 18.gw),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildActionButton(
            context,
            icon: Assets.usersIcon,
            label: 'downloadList'.tr(),
            onTap: () => _navigateToSubordinateList(context),
          ),
          _buildActionButton(
            context,
            icon: Assets.rebateIcon,
            label: 'rebateDetails'.tr(),
            onTap: () => _navigateToRebateDetails(context),
          ),
          _buildActionButton(
            context,
            icon: Assets.inviteIcon,
            label: 'generateLink'.tr(),
            onTap: () => _generateInviteLink(context, state),
          ),
        ],
      ),
    );
  }

  /// Build individual action button
  Widget _buildActionButton(
    BuildContext context, {
    required String icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 30.gw,
            height: 30.gw,
            child: IconHelper.loadAsset(
              icon,
              width: 24.gw,
              height: 24.gw,
            ),
          ),
          SizedBox(height: 8.gw),
          Text(
            label,
            style: context.textTheme.primary.copyWith(color: const Color(0xFFFEEAAB)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Navigate to subordinate list screen
  void _navigateToSubordinateList(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SubordinateListScreen(
          skinStyle: widget.skinStyle,
        ),
      ),
    );
  }

  /// Navigate to rebate details screen
  void _navigateToRebateDetails(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RebateDetailsScreen(
          skinStyle: widget.skinStyle,
        ),
      ),
    );
  }

  /// Generate invite link
  void _generateInviteLink(BuildContext context, InviteState state) {
    final inviteCode = state.inviteDetail?.inviteCode ?? '';
    final inviteLink = state.customInviteLink ?? '${Urls.inviteLink}$inviteCode';

    Clipboard.setData(ClipboardData(text: inviteLink));
    Helper.showFlutterToast('inviteLinkCopiedToClipboard'.tr());
  }
}
