import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/string_util.dart';
import 'package:gp_stock_app/features/invite/domain/models/vip_level_table_model.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';

class AgentLevelInfoCell extends StatelessWidget {
  final VipLevelTableModel model;

  const AgentLevelInfoCell({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return ShadowBox(
        borderRadius: BorderRadius.circular(8.gw),
        child: Column(
          children: [
            _buildOrderInfoRow(context, 'agentLevel'.tr(), 'VIP${model.level ?? '-'}'),
            _buildOrderInfoRow(context, 'rebateRatio'.tr(), '${model.rebateRate?.formattedMoney}%'),
            _buildOrderInfoRow(context, 'interestRatio'.tr(), '${model.interestRate?.formattedMoney}%'),
            _buildOrderInfoRow(context, 'rebateConditions'.tr(),
                '${'validMembers'.tr()}${model.requiredInviteCount}\n${'cumulativeRecharge'.tr()}${model.requiredRechargeAmount?.formattedMoney}',
                valueFontSize: 10.gsp),
          ],
        ));
  }

  Widget _buildOrderInfoRow(BuildContext context, String label, String value, {double? valueFontSize}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.gw),
      child: Row(
        // crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: context.textTheme.tertiary.copyWith(),
          ),
          SizedBox(width: 15.gw),
          Expanded(
            child: Text(
              value,
              style: context.textTheme.title.copyWith(fontSize: valueFontSize, color: Color(0xff222222)),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }
}
