import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../../shared/widgets/shimmer/shimmer_widget.dart';

class InviteScreenShimmer extends StatelessWidget {
  const InviteScreenShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.colorTheme.inviteBackgroundEnd,
      ),
      child: Column(
        children: [
          // Action buttons and stats cards section
          Container(
            margin: EdgeInsets.only(top: 10.gw, left: 10.gw, right: 10.gw, bottom: 0.gw),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(26)),
            ),
            padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 16.gw),
            child: ShimmerWidget(
              child: Column(
                children: [
                  _buildActionButtonsShimmer(),
                  _buildStatsCardsShimmer(),
                ],
              ),
            ),
          ),

          Expanded(
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: ShimmerWidget(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildVipLevelTableShimmer(),
                      SizedBox(height: 20.gw),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build action buttons shimmer
  Widget _buildActionButtonsShimmer() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 20.gw),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildSingleActionButtonShimmer(),
          _buildSingleActionButtonShimmer(),
          _buildSingleActionButtonShimmer(),
        ],
      ),
    );
  }

  /// Build single action button shimmer
  Widget _buildSingleActionButtonShimmer() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 30.gw,
          height: 30.gw,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(4.gr),
          ),
        ),
        SizedBox(height: 8.gw),
        Container(
          width: 60.gw,
          height: 14.gw,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(4.gr),
          ),
        ),
      ],
    );
  }

  /// Build stats cards shimmer
  Widget _buildStatsCardsShimmer() {
    return Container(
      padding: EdgeInsets.only(top: 8.gw, bottom: 8.gw, left: 10.gw),
      margin: EdgeInsets.symmetric(horizontal: 10.gw),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(16.gr),
      ),
      child: Column(
        children: [
          // VIP level indicator shimmer
          Container(
            width: 60.gw,
            height: 16.gw,
            decoration: BoxDecoration(
              color: Colors.grey[400],
              borderRadius: BorderRadius.circular(4.gr),
            ),
          ),
          SizedBox(height: 16.gw),
          // Stats grid shimmer
          Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    _buildStatItemShimmer(),
                    SizedBox(height: 16.gw),
                    _buildStatItemShimmer(),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  children: [
                    _buildStatItemShimmer(),
                    SizedBox(height: 16.gw),
                    _buildStatItemShimmer(),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build single stat item shimmer
  Widget _buildStatItemShimmer() {
    return Container(
      padding: EdgeInsets.fromLTRB(16.gr, 16.gr, 8.gr, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 80.gw,
            height: 12.gw,
            decoration: BoxDecoration(
              color: Colors.grey[400],
              borderRadius: BorderRadius.circular(4.gr),
            ),
          ),
          SizedBox(height: 10.gw),
          Container(
            width: 60.gw,
            height: 18.gw,
            decoration: BoxDecoration(
              color: Colors.grey[400],
              borderRadius: BorderRadius.circular(4.gr),
            ),
          ),
        ],
      ),
    );
  }

  /// Build VIP level table shimmer
  Widget _buildVipLevelTableShimmer() {
    return Container(
      margin: EdgeInsets.all(12.gr),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title shimmer
          Container(
            width: 150.gw,
            height: 16.gw,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(4.gr),
            ),
          ),
          SizedBox(height: 16.gw),
          // Table header shimmer
          Container(
            padding: EdgeInsets.symmetric(vertical: 8.gw),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    width: 60.gw,
                    height: 14.gw,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Container(
                    width: 80.gw,
                    height: 14.gw,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Container(
                    width: 60.gw,
                    height: 14.gw,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Container(
                    width: 60.gw,
                    height: 14.gw,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Table rows shimmer
          ...List.generate(3, (index) => _buildVipLevelRowShimmer()),
        ],
      ),
    );
  }

  /// Build single VIP level row shimmer
  Widget _buildVipLevelRowShimmer() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.gw),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!, width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Container(
              width: 40.gw,
              height: 16.gw,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(4.gr),
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Column(
              children: [
                Container(
                  width: 100.gw,
                  height: 11.gw,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4.gr),
                  ),
                ),
                SizedBox(height: 4.gw),
                Container(
                  width: 80.gw,
                  height: 11.gw,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4.gr),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              width: 40.gw,
              height: 14.gw,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(4.gr),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              width: 40.gw,
              height: 14.gw,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(4.gr),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
