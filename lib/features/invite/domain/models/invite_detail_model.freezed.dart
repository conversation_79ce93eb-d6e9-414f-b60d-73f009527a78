// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'invite_detail_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

InviteDetailModel _$InviteDetailModelFromJson(Map<String, dynamic> json) {
  return _InviteDetailModel.fromJson(json);
}

/// @nodoc
mixin _$InviteDetailModel {
  int? get currentVipLevel => throw _privateConstructorUsedError;
  double? get interestRebateRate => throw _privateConstructorUsedError;
  List<SubordinateModel>? get subordinateList =>
      throw _privateConstructorUsedError;
  double? get totalRebate => throw _privateConstructorUsedError;
  double? get totalRechargeAmount => throw _privateConstructorUsedError;
  int? get totalSatisfiedInviteCount => throw _privateConstructorUsedError;
  int? get totalSubordinates => throw _privateConstructorUsedError;
  double? get tradingRebateRate => throw _privateConstructorUsedError;
  List<VipLevelTableModel>? get vipLevelTableList =>
      throw _privateConstructorUsedError;
  String? get inviteCode => throw _privateConstructorUsedError;
  String? get customInviteLink => throw _privateConstructorUsedError;

  /// Serializes this InviteDetailModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of InviteDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InviteDetailModelCopyWith<InviteDetailModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InviteDetailModelCopyWith<$Res> {
  factory $InviteDetailModelCopyWith(
          InviteDetailModel value, $Res Function(InviteDetailModel) then) =
      _$InviteDetailModelCopyWithImpl<$Res, InviteDetailModel>;
  @useResult
  $Res call(
      {int? currentVipLevel,
      double? interestRebateRate,
      List<SubordinateModel>? subordinateList,
      double? totalRebate,
      double? totalRechargeAmount,
      int? totalSatisfiedInviteCount,
      int? totalSubordinates,
      double? tradingRebateRate,
      List<VipLevelTableModel>? vipLevelTableList,
      String? inviteCode,
      String? customInviteLink});
}

/// @nodoc
class _$InviteDetailModelCopyWithImpl<$Res, $Val extends InviteDetailModel>
    implements $InviteDetailModelCopyWith<$Res> {
  _$InviteDetailModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InviteDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentVipLevel = freezed,
    Object? interestRebateRate = freezed,
    Object? subordinateList = freezed,
    Object? totalRebate = freezed,
    Object? totalRechargeAmount = freezed,
    Object? totalSatisfiedInviteCount = freezed,
    Object? totalSubordinates = freezed,
    Object? tradingRebateRate = freezed,
    Object? vipLevelTableList = freezed,
    Object? inviteCode = freezed,
    Object? customInviteLink = freezed,
  }) {
    return _then(_value.copyWith(
      currentVipLevel: freezed == currentVipLevel
          ? _value.currentVipLevel
          : currentVipLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      interestRebateRate: freezed == interestRebateRate
          ? _value.interestRebateRate
          : interestRebateRate // ignore: cast_nullable_to_non_nullable
              as double?,
      subordinateList: freezed == subordinateList
          ? _value.subordinateList
          : subordinateList // ignore: cast_nullable_to_non_nullable
              as List<SubordinateModel>?,
      totalRebate: freezed == totalRebate
          ? _value.totalRebate
          : totalRebate // ignore: cast_nullable_to_non_nullable
              as double?,
      totalRechargeAmount: freezed == totalRechargeAmount
          ? _value.totalRechargeAmount
          : totalRechargeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      totalSatisfiedInviteCount: freezed == totalSatisfiedInviteCount
          ? _value.totalSatisfiedInviteCount
          : totalSatisfiedInviteCount // ignore: cast_nullable_to_non_nullable
              as int?,
      totalSubordinates: freezed == totalSubordinates
          ? _value.totalSubordinates
          : totalSubordinates // ignore: cast_nullable_to_non_nullable
              as int?,
      tradingRebateRate: freezed == tradingRebateRate
          ? _value.tradingRebateRate
          : tradingRebateRate // ignore: cast_nullable_to_non_nullable
              as double?,
      vipLevelTableList: freezed == vipLevelTableList
          ? _value.vipLevelTableList
          : vipLevelTableList // ignore: cast_nullable_to_non_nullable
              as List<VipLevelTableModel>?,
      inviteCode: freezed == inviteCode
          ? _value.inviteCode
          : inviteCode // ignore: cast_nullable_to_non_nullable
              as String?,
      customInviteLink: freezed == customInviteLink
          ? _value.customInviteLink
          : customInviteLink // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InviteDetailModelImplCopyWith<$Res>
    implements $InviteDetailModelCopyWith<$Res> {
  factory _$$InviteDetailModelImplCopyWith(_$InviteDetailModelImpl value,
          $Res Function(_$InviteDetailModelImpl) then) =
      __$$InviteDetailModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? currentVipLevel,
      double? interestRebateRate,
      List<SubordinateModel>? subordinateList,
      double? totalRebate,
      double? totalRechargeAmount,
      int? totalSatisfiedInviteCount,
      int? totalSubordinates,
      double? tradingRebateRate,
      List<VipLevelTableModel>? vipLevelTableList,
      String? inviteCode,
      String? customInviteLink});
}

/// @nodoc
class __$$InviteDetailModelImplCopyWithImpl<$Res>
    extends _$InviteDetailModelCopyWithImpl<$Res, _$InviteDetailModelImpl>
    implements _$$InviteDetailModelImplCopyWith<$Res> {
  __$$InviteDetailModelImplCopyWithImpl(_$InviteDetailModelImpl _value,
      $Res Function(_$InviteDetailModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of InviteDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentVipLevel = freezed,
    Object? interestRebateRate = freezed,
    Object? subordinateList = freezed,
    Object? totalRebate = freezed,
    Object? totalRechargeAmount = freezed,
    Object? totalSatisfiedInviteCount = freezed,
    Object? totalSubordinates = freezed,
    Object? tradingRebateRate = freezed,
    Object? vipLevelTableList = freezed,
    Object? inviteCode = freezed,
    Object? customInviteLink = freezed,
  }) {
    return _then(_$InviteDetailModelImpl(
      currentVipLevel: freezed == currentVipLevel
          ? _value.currentVipLevel
          : currentVipLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      interestRebateRate: freezed == interestRebateRate
          ? _value.interestRebateRate
          : interestRebateRate // ignore: cast_nullable_to_non_nullable
              as double?,
      subordinateList: freezed == subordinateList
          ? _value._subordinateList
          : subordinateList // ignore: cast_nullable_to_non_nullable
              as List<SubordinateModel>?,
      totalRebate: freezed == totalRebate
          ? _value.totalRebate
          : totalRebate // ignore: cast_nullable_to_non_nullable
              as double?,
      totalRechargeAmount: freezed == totalRechargeAmount
          ? _value.totalRechargeAmount
          : totalRechargeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      totalSatisfiedInviteCount: freezed == totalSatisfiedInviteCount
          ? _value.totalSatisfiedInviteCount
          : totalSatisfiedInviteCount // ignore: cast_nullable_to_non_nullable
              as int?,
      totalSubordinates: freezed == totalSubordinates
          ? _value.totalSubordinates
          : totalSubordinates // ignore: cast_nullable_to_non_nullable
              as int?,
      tradingRebateRate: freezed == tradingRebateRate
          ? _value.tradingRebateRate
          : tradingRebateRate // ignore: cast_nullable_to_non_nullable
              as double?,
      vipLevelTableList: freezed == vipLevelTableList
          ? _value._vipLevelTableList
          : vipLevelTableList // ignore: cast_nullable_to_non_nullable
              as List<VipLevelTableModel>?,
      inviteCode: freezed == inviteCode
          ? _value.inviteCode
          : inviteCode // ignore: cast_nullable_to_non_nullable
              as String?,
      customInviteLink: freezed == customInviteLink
          ? _value.customInviteLink
          : customInviteLink // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$InviteDetailModelImpl implements _InviteDetailModel {
  const _$InviteDetailModelImpl(
      {this.currentVipLevel,
      this.interestRebateRate,
      final List<SubordinateModel>? subordinateList,
      this.totalRebate,
      this.totalRechargeAmount,
      this.totalSatisfiedInviteCount,
      this.totalSubordinates,
      this.tradingRebateRate,
      final List<VipLevelTableModel>? vipLevelTableList,
      this.inviteCode,
      this.customInviteLink})
      : _subordinateList = subordinateList,
        _vipLevelTableList = vipLevelTableList;

  factory _$InviteDetailModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$InviteDetailModelImplFromJson(json);

  @override
  final int? currentVipLevel;
  @override
  final double? interestRebateRate;
  final List<SubordinateModel>? _subordinateList;
  @override
  List<SubordinateModel>? get subordinateList {
    final value = _subordinateList;
    if (value == null) return null;
    if (_subordinateList is EqualUnmodifiableListView) return _subordinateList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final double? totalRebate;
  @override
  final double? totalRechargeAmount;
  @override
  final int? totalSatisfiedInviteCount;
  @override
  final int? totalSubordinates;
  @override
  final double? tradingRebateRate;
  final List<VipLevelTableModel>? _vipLevelTableList;
  @override
  List<VipLevelTableModel>? get vipLevelTableList {
    final value = _vipLevelTableList;
    if (value == null) return null;
    if (_vipLevelTableList is EqualUnmodifiableListView)
      return _vipLevelTableList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? inviteCode;
  @override
  final String? customInviteLink;

  @override
  String toString() {
    return 'InviteDetailModel(currentVipLevel: $currentVipLevel, interestRebateRate: $interestRebateRate, subordinateList: $subordinateList, totalRebate: $totalRebate, totalRechargeAmount: $totalRechargeAmount, totalSatisfiedInviteCount: $totalSatisfiedInviteCount, totalSubordinates: $totalSubordinates, tradingRebateRate: $tradingRebateRate, vipLevelTableList: $vipLevelTableList, inviteCode: $inviteCode, customInviteLink: $customInviteLink)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InviteDetailModelImpl &&
            (identical(other.currentVipLevel, currentVipLevel) ||
                other.currentVipLevel == currentVipLevel) &&
            (identical(other.interestRebateRate, interestRebateRate) ||
                other.interestRebateRate == interestRebateRate) &&
            const DeepCollectionEquality()
                .equals(other._subordinateList, _subordinateList) &&
            (identical(other.totalRebate, totalRebate) ||
                other.totalRebate == totalRebate) &&
            (identical(other.totalRechargeAmount, totalRechargeAmount) ||
                other.totalRechargeAmount == totalRechargeAmount) &&
            (identical(other.totalSatisfiedInviteCount,
                    totalSatisfiedInviteCount) ||
                other.totalSatisfiedInviteCount == totalSatisfiedInviteCount) &&
            (identical(other.totalSubordinates, totalSubordinates) ||
                other.totalSubordinates == totalSubordinates) &&
            (identical(other.tradingRebateRate, tradingRebateRate) ||
                other.tradingRebateRate == tradingRebateRate) &&
            const DeepCollectionEquality()
                .equals(other._vipLevelTableList, _vipLevelTableList) &&
            (identical(other.inviteCode, inviteCode) ||
                other.inviteCode == inviteCode) &&
            (identical(other.customInviteLink, customInviteLink) ||
                other.customInviteLink == customInviteLink));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentVipLevel,
      interestRebateRate,
      const DeepCollectionEquality().hash(_subordinateList),
      totalRebate,
      totalRechargeAmount,
      totalSatisfiedInviteCount,
      totalSubordinates,
      tradingRebateRate,
      const DeepCollectionEquality().hash(_vipLevelTableList),
      inviteCode,
      customInviteLink);

  /// Create a copy of InviteDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InviteDetailModelImplCopyWith<_$InviteDetailModelImpl> get copyWith =>
      __$$InviteDetailModelImplCopyWithImpl<_$InviteDetailModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InviteDetailModelImplToJson(
      this,
    );
  }
}

abstract class _InviteDetailModel implements InviteDetailModel {
  const factory _InviteDetailModel(
      {final int? currentVipLevel,
      final double? interestRebateRate,
      final List<SubordinateModel>? subordinateList,
      final double? totalRebate,
      final double? totalRechargeAmount,
      final int? totalSatisfiedInviteCount,
      final int? totalSubordinates,
      final double? tradingRebateRate,
      final List<VipLevelTableModel>? vipLevelTableList,
      final String? inviteCode,
      final String? customInviteLink}) = _$InviteDetailModelImpl;

  factory _InviteDetailModel.fromJson(Map<String, dynamic> json) =
      _$InviteDetailModelImpl.fromJson;

  @override
  int? get currentVipLevel;
  @override
  double? get interestRebateRate;
  @override
  List<SubordinateModel>? get subordinateList;
  @override
  double? get totalRebate;
  @override
  double? get totalRechargeAmount;
  @override
  int? get totalSatisfiedInviteCount;
  @override
  int? get totalSubordinates;
  @override
  double? get tradingRebateRate;
  @override
  List<VipLevelTableModel>? get vipLevelTableList;
  @override
  String? get inviteCode;
  @override
  String? get customInviteLink;

  /// Create a copy of InviteDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InviteDetailModelImplCopyWith<_$InviteDetailModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
