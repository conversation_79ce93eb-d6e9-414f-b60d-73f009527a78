import 'package:freezed_annotation/freezed_annotation.dart';

part 'subordinate_model.freezed.dart';
part 'subordinate_model.g.dart';

@freezed
class SubordinateModel with _$SubordinateModel {
  const factory SubordinateModel({
    SubordinateDetailModel? detail,
    String? displayName,
    bool? isValid,
    bool? status,
    double? totalRebateFromSubordinate,
    int? type,
    int? userId,
  }) = _SubordinateModel;

  factory SubordinateModel.fromJson(Map<String, dynamic> json) => _$SubordinateModelFromJson(json);
}

@freezed
class SubordinateDetailModel with _$SubordinateDetailModel {
  const factory SubordinateDetailModel({
    double? balance,
    double? frozenAmount,
    double? margin,
    String? maskedIdCard,
    String? maskedMobile,
    String? memberName,
  }) = _SubordinateDetailModel;

  factory SubordinateDetailModel.fromJson(Map<String, dynamic> json) => _$SubordinateDetailModelFromJson(json);
}

@freezed
class SubordinatePaginatedResponse with _$SubordinatePaginatedResponse {
  const factory SubordinatePaginatedResponse({
    required List<SubordinateModel> records,
    required int current,
    required int pages,
    required int size,
    required int total,
  }) = _SubordinatePaginatedResponse;

  factory SubordinatePaginatedResponse.fromJson(Map<String, dynamic> json) =>
      _$SubordinatePaginatedResponseFromJson(json);
}
