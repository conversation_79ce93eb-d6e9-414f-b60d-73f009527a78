import 'package:freezed_annotation/freezed_annotation.dart';

part 'vip_level_table_model.freezed.dart';
part 'vip_level_table_model.g.dart';

@freezed
class VipLevelTableModel with _$VipLevelTableModel {
  const factory VipLevelTableModel({
    double? interestRate,
    int? level,
    double? rebateRate,
    int? requiredInviteCount,
    double? requiredInviteRechargeAmount,
    double? requiredRechargeAmount,
    int? satisfiedConditions,
    int? satisfiedInviteCount,
    int? totalConditions,
    int? totalInviteCount,
    double? totalInviteRechargeAmount,
    double? totalRechargeAmount,
  }) = _VipLevelTableModel;

  factory VipLevelTableModel.fromJson(Map<String, dynamic> json) => _$VipLevelTableModelFromJson(json);
}
