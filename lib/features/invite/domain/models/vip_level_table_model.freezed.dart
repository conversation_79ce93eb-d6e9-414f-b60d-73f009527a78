// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vip_level_table_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VipLevelTableModel _$VipLevelTableModelFromJson(Map<String, dynamic> json) {
  return _VipLevelTableModel.fromJson(json);
}

/// @nodoc
mixin _$VipLevelTableModel {
  double? get interestRate => throw _privateConstructorUsedError;
  int? get level => throw _privateConstructorUsedError;
  double? get rebateRate => throw _privateConstructorUsedError;
  int? get requiredInviteCount => throw _privateConstructorUsedError;
  double? get requiredInviteRechargeAmount =>
      throw _privateConstructorUsedError;
  double? get requiredRechargeAmount => throw _privateConstructorUsedError;
  int? get satisfiedConditions => throw _privateConstructorUsedError;
  int? get satisfiedInviteCount => throw _privateConstructorUsedError;
  int? get totalConditions => throw _privateConstructorUsedError;
  int? get totalInviteCount => throw _privateConstructorUsedError;
  double? get totalInviteRechargeAmount => throw _privateConstructorUsedError;
  double? get totalRechargeAmount => throw _privateConstructorUsedError;

  /// Serializes this VipLevelTableModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VipLevelTableModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VipLevelTableModelCopyWith<VipLevelTableModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VipLevelTableModelCopyWith<$Res> {
  factory $VipLevelTableModelCopyWith(
          VipLevelTableModel value, $Res Function(VipLevelTableModel) then) =
      _$VipLevelTableModelCopyWithImpl<$Res, VipLevelTableModel>;
  @useResult
  $Res call(
      {double? interestRate,
      int? level,
      double? rebateRate,
      int? requiredInviteCount,
      double? requiredInviteRechargeAmount,
      double? requiredRechargeAmount,
      int? satisfiedConditions,
      int? satisfiedInviteCount,
      int? totalConditions,
      int? totalInviteCount,
      double? totalInviteRechargeAmount,
      double? totalRechargeAmount});
}

/// @nodoc
class _$VipLevelTableModelCopyWithImpl<$Res, $Val extends VipLevelTableModel>
    implements $VipLevelTableModelCopyWith<$Res> {
  _$VipLevelTableModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VipLevelTableModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? interestRate = freezed,
    Object? level = freezed,
    Object? rebateRate = freezed,
    Object? requiredInviteCount = freezed,
    Object? requiredInviteRechargeAmount = freezed,
    Object? requiredRechargeAmount = freezed,
    Object? satisfiedConditions = freezed,
    Object? satisfiedInviteCount = freezed,
    Object? totalConditions = freezed,
    Object? totalInviteCount = freezed,
    Object? totalInviteRechargeAmount = freezed,
    Object? totalRechargeAmount = freezed,
  }) {
    return _then(_value.copyWith(
      interestRate: freezed == interestRate
          ? _value.interestRate
          : interestRate // ignore: cast_nullable_to_non_nullable
              as double?,
      level: freezed == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int?,
      rebateRate: freezed == rebateRate
          ? _value.rebateRate
          : rebateRate // ignore: cast_nullable_to_non_nullable
              as double?,
      requiredInviteCount: freezed == requiredInviteCount
          ? _value.requiredInviteCount
          : requiredInviteCount // ignore: cast_nullable_to_non_nullable
              as int?,
      requiredInviteRechargeAmount: freezed == requiredInviteRechargeAmount
          ? _value.requiredInviteRechargeAmount
          : requiredInviteRechargeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      requiredRechargeAmount: freezed == requiredRechargeAmount
          ? _value.requiredRechargeAmount
          : requiredRechargeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      satisfiedConditions: freezed == satisfiedConditions
          ? _value.satisfiedConditions
          : satisfiedConditions // ignore: cast_nullable_to_non_nullable
              as int?,
      satisfiedInviteCount: freezed == satisfiedInviteCount
          ? _value.satisfiedInviteCount
          : satisfiedInviteCount // ignore: cast_nullable_to_non_nullable
              as int?,
      totalConditions: freezed == totalConditions
          ? _value.totalConditions
          : totalConditions // ignore: cast_nullable_to_non_nullable
              as int?,
      totalInviteCount: freezed == totalInviteCount
          ? _value.totalInviteCount
          : totalInviteCount // ignore: cast_nullable_to_non_nullable
              as int?,
      totalInviteRechargeAmount: freezed == totalInviteRechargeAmount
          ? _value.totalInviteRechargeAmount
          : totalInviteRechargeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      totalRechargeAmount: freezed == totalRechargeAmount
          ? _value.totalRechargeAmount
          : totalRechargeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VipLevelTableModelImplCopyWith<$Res>
    implements $VipLevelTableModelCopyWith<$Res> {
  factory _$$VipLevelTableModelImplCopyWith(_$VipLevelTableModelImpl value,
          $Res Function(_$VipLevelTableModelImpl) then) =
      __$$VipLevelTableModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double? interestRate,
      int? level,
      double? rebateRate,
      int? requiredInviteCount,
      double? requiredInviteRechargeAmount,
      double? requiredRechargeAmount,
      int? satisfiedConditions,
      int? satisfiedInviteCount,
      int? totalConditions,
      int? totalInviteCount,
      double? totalInviteRechargeAmount,
      double? totalRechargeAmount});
}

/// @nodoc
class __$$VipLevelTableModelImplCopyWithImpl<$Res>
    extends _$VipLevelTableModelCopyWithImpl<$Res, _$VipLevelTableModelImpl>
    implements _$$VipLevelTableModelImplCopyWith<$Res> {
  __$$VipLevelTableModelImplCopyWithImpl(_$VipLevelTableModelImpl _value,
      $Res Function(_$VipLevelTableModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of VipLevelTableModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? interestRate = freezed,
    Object? level = freezed,
    Object? rebateRate = freezed,
    Object? requiredInviteCount = freezed,
    Object? requiredInviteRechargeAmount = freezed,
    Object? requiredRechargeAmount = freezed,
    Object? satisfiedConditions = freezed,
    Object? satisfiedInviteCount = freezed,
    Object? totalConditions = freezed,
    Object? totalInviteCount = freezed,
    Object? totalInviteRechargeAmount = freezed,
    Object? totalRechargeAmount = freezed,
  }) {
    return _then(_$VipLevelTableModelImpl(
      interestRate: freezed == interestRate
          ? _value.interestRate
          : interestRate // ignore: cast_nullable_to_non_nullable
              as double?,
      level: freezed == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int?,
      rebateRate: freezed == rebateRate
          ? _value.rebateRate
          : rebateRate // ignore: cast_nullable_to_non_nullable
              as double?,
      requiredInviteCount: freezed == requiredInviteCount
          ? _value.requiredInviteCount
          : requiredInviteCount // ignore: cast_nullable_to_non_nullable
              as int?,
      requiredInviteRechargeAmount: freezed == requiredInviteRechargeAmount
          ? _value.requiredInviteRechargeAmount
          : requiredInviteRechargeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      requiredRechargeAmount: freezed == requiredRechargeAmount
          ? _value.requiredRechargeAmount
          : requiredRechargeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      satisfiedConditions: freezed == satisfiedConditions
          ? _value.satisfiedConditions
          : satisfiedConditions // ignore: cast_nullable_to_non_nullable
              as int?,
      satisfiedInviteCount: freezed == satisfiedInviteCount
          ? _value.satisfiedInviteCount
          : satisfiedInviteCount // ignore: cast_nullable_to_non_nullable
              as int?,
      totalConditions: freezed == totalConditions
          ? _value.totalConditions
          : totalConditions // ignore: cast_nullable_to_non_nullable
              as int?,
      totalInviteCount: freezed == totalInviteCount
          ? _value.totalInviteCount
          : totalInviteCount // ignore: cast_nullable_to_non_nullable
              as int?,
      totalInviteRechargeAmount: freezed == totalInviteRechargeAmount
          ? _value.totalInviteRechargeAmount
          : totalInviteRechargeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      totalRechargeAmount: freezed == totalRechargeAmount
          ? _value.totalRechargeAmount
          : totalRechargeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VipLevelTableModelImpl implements _VipLevelTableModel {
  const _$VipLevelTableModelImpl(
      {this.interestRate,
      this.level,
      this.rebateRate,
      this.requiredInviteCount,
      this.requiredInviteRechargeAmount,
      this.requiredRechargeAmount,
      this.satisfiedConditions,
      this.satisfiedInviteCount,
      this.totalConditions,
      this.totalInviteCount,
      this.totalInviteRechargeAmount,
      this.totalRechargeAmount});

  factory _$VipLevelTableModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$VipLevelTableModelImplFromJson(json);

  @override
  final double? interestRate;
  @override
  final int? level;
  @override
  final double? rebateRate;
  @override
  final int? requiredInviteCount;
  @override
  final double? requiredInviteRechargeAmount;
  @override
  final double? requiredRechargeAmount;
  @override
  final int? satisfiedConditions;
  @override
  final int? satisfiedInviteCount;
  @override
  final int? totalConditions;
  @override
  final int? totalInviteCount;
  @override
  final double? totalInviteRechargeAmount;
  @override
  final double? totalRechargeAmount;

  @override
  String toString() {
    return 'VipLevelTableModel(interestRate: $interestRate, level: $level, rebateRate: $rebateRate, requiredInviteCount: $requiredInviteCount, requiredInviteRechargeAmount: $requiredInviteRechargeAmount, requiredRechargeAmount: $requiredRechargeAmount, satisfiedConditions: $satisfiedConditions, satisfiedInviteCount: $satisfiedInviteCount, totalConditions: $totalConditions, totalInviteCount: $totalInviteCount, totalInviteRechargeAmount: $totalInviteRechargeAmount, totalRechargeAmount: $totalRechargeAmount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VipLevelTableModelImpl &&
            (identical(other.interestRate, interestRate) ||
                other.interestRate == interestRate) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.rebateRate, rebateRate) ||
                other.rebateRate == rebateRate) &&
            (identical(other.requiredInviteCount, requiredInviteCount) ||
                other.requiredInviteCount == requiredInviteCount) &&
            (identical(other.requiredInviteRechargeAmount,
                    requiredInviteRechargeAmount) ||
                other.requiredInviteRechargeAmount ==
                    requiredInviteRechargeAmount) &&
            (identical(other.requiredRechargeAmount, requiredRechargeAmount) ||
                other.requiredRechargeAmount == requiredRechargeAmount) &&
            (identical(other.satisfiedConditions, satisfiedConditions) ||
                other.satisfiedConditions == satisfiedConditions) &&
            (identical(other.satisfiedInviteCount, satisfiedInviteCount) ||
                other.satisfiedInviteCount == satisfiedInviteCount) &&
            (identical(other.totalConditions, totalConditions) ||
                other.totalConditions == totalConditions) &&
            (identical(other.totalInviteCount, totalInviteCount) ||
                other.totalInviteCount == totalInviteCount) &&
            (identical(other.totalInviteRechargeAmount,
                    totalInviteRechargeAmount) ||
                other.totalInviteRechargeAmount == totalInviteRechargeAmount) &&
            (identical(other.totalRechargeAmount, totalRechargeAmount) ||
                other.totalRechargeAmount == totalRechargeAmount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      interestRate,
      level,
      rebateRate,
      requiredInviteCount,
      requiredInviteRechargeAmount,
      requiredRechargeAmount,
      satisfiedConditions,
      satisfiedInviteCount,
      totalConditions,
      totalInviteCount,
      totalInviteRechargeAmount,
      totalRechargeAmount);

  /// Create a copy of VipLevelTableModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VipLevelTableModelImplCopyWith<_$VipLevelTableModelImpl> get copyWith =>
      __$$VipLevelTableModelImplCopyWithImpl<_$VipLevelTableModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VipLevelTableModelImplToJson(
      this,
    );
  }
}

abstract class _VipLevelTableModel implements VipLevelTableModel {
  const factory _VipLevelTableModel(
      {final double? interestRate,
      final int? level,
      final double? rebateRate,
      final int? requiredInviteCount,
      final double? requiredInviteRechargeAmount,
      final double? requiredRechargeAmount,
      final int? satisfiedConditions,
      final int? satisfiedInviteCount,
      final int? totalConditions,
      final int? totalInviteCount,
      final double? totalInviteRechargeAmount,
      final double? totalRechargeAmount}) = _$VipLevelTableModelImpl;

  factory _VipLevelTableModel.fromJson(Map<String, dynamic> json) =
      _$VipLevelTableModelImpl.fromJson;

  @override
  double? get interestRate;
  @override
  int? get level;
  @override
  double? get rebateRate;
  @override
  int? get requiredInviteCount;
  @override
  double? get requiredInviteRechargeAmount;
  @override
  double? get requiredRechargeAmount;
  @override
  int? get satisfiedConditions;
  @override
  int? get satisfiedInviteCount;
  @override
  int? get totalConditions;
  @override
  int? get totalInviteCount;
  @override
  double? get totalInviteRechargeAmount;
  @override
  double? get totalRechargeAmount;

  /// Create a copy of VipLevelTableModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VipLevelTableModelImplCopyWith<_$VipLevelTableModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
