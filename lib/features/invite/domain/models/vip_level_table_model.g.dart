// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vip_level_table_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VipLevelTableModelImpl _$$VipLevelTableModelImplFromJson(
        Map<String, dynamic> json) =>
    _$VipLevelTableModelImpl(
      interestRate: (json['interestRate'] as num?)?.toDouble(),
      level: (json['level'] as num?)?.toInt(),
      rebateRate: (json['rebateRate'] as num?)?.toDouble(),
      requiredInviteCount: (json['requiredInviteCount'] as num?)?.toInt(),
      requiredInviteRechargeAmount:
          (json['requiredInviteRechargeAmount'] as num?)?.toDouble(),
      requiredRechargeAmount:
          (json['requiredRechargeAmount'] as num?)?.toDouble(),
      satisfiedConditions: (json['satisfiedConditions'] as num?)?.toInt(),
      satisfiedInviteCount: (json['satisfiedInviteCount'] as num?)?.toInt(),
      totalConditions: (json['totalConditions'] as num?)?.toInt(),
      totalInviteCount: (json['totalInviteCount'] as num?)?.toInt(),
      totalInviteRechargeAmount:
          (json['totalInviteRechargeAmount'] as num?)?.toDouble(),
      totalRechargeAmount: (json['totalRechargeAmount'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$VipLevelTableModelImplToJson(
        _$VipLevelTableModelImpl instance) =>
    <String, dynamic>{
      'interestRate': instance.interestRate,
      'level': instance.level,
      'rebateRate': instance.rebateRate,
      'requiredInviteCount': instance.requiredInviteCount,
      'requiredInviteRechargeAmount': instance.requiredInviteRechargeAmount,
      'requiredRechargeAmount': instance.requiredRechargeAmount,
      'satisfiedConditions': instance.satisfiedConditions,
      'satisfiedInviteCount': instance.satisfiedInviteCount,
      'totalConditions': instance.totalConditions,
      'totalInviteCount': instance.totalInviteCount,
      'totalInviteRechargeAmount': instance.totalInviteRechargeAmount,
      'totalRechargeAmount': instance.totalRechargeAmount,
    };
