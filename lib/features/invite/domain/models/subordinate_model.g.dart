// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subordinate_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SubordinateModelImpl _$$SubordinateModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SubordinateModelImpl(
      detail: json['detail'] == null
          ? null
          : SubordinateDetailModel.fromJson(
              json['detail'] as Map<String, dynamic>),
      displayName: json['displayName'] as String?,
      isValid: json['isValid'] as bool?,
      status: json['status'] as bool?,
      totalRebateFromSubordinate:
          (json['totalRebateFromSubordinate'] as num?)?.toDouble(),
      type: (json['type'] as num?)?.toInt(),
      userId: (json['userId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$SubordinateModelImplToJson(
        _$SubordinateModelImpl instance) =>
    <String, dynamic>{
      'detail': instance.detail,
      'displayName': instance.displayName,
      'isValid': instance.isValid,
      'status': instance.status,
      'totalRebateFromSubordinate': instance.totalRebateFromSubordinate,
      'type': instance.type,
      'userId': instance.userId,
    };

_$SubordinateDetailModelImpl _$$SubordinateDetailModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SubordinateDetailModelImpl(
      balance: (json['balance'] as num?)?.toDouble(),
      frozenAmount: (json['frozenAmount'] as num?)?.toDouble(),
      margin: (json['margin'] as num?)?.toDouble(),
      maskedIdCard: json['maskedIdCard'] as String?,
      maskedMobile: json['maskedMobile'] as String?,
      memberName: json['memberName'] as String?,
    );

Map<String, dynamic> _$$SubordinateDetailModelImplToJson(
        _$SubordinateDetailModelImpl instance) =>
    <String, dynamic>{
      'balance': instance.balance,
      'frozenAmount': instance.frozenAmount,
      'margin': instance.margin,
      'maskedIdCard': instance.maskedIdCard,
      'maskedMobile': instance.maskedMobile,
      'memberName': instance.memberName,
    };

_$SubordinatePaginatedResponseImpl _$$SubordinatePaginatedResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$SubordinatePaginatedResponseImpl(
      records: (json['records'] as List<dynamic>)
          .map((e) => SubordinateModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      current: (json['current'] as num).toInt(),
      pages: (json['pages'] as num).toInt(),
      size: (json['size'] as num).toInt(),
      total: (json['total'] as num).toInt(),
    );

Map<String, dynamic> _$$SubordinatePaginatedResponseImplToJson(
        _$SubordinatePaginatedResponseImpl instance) =>
    <String, dynamic>{
      'records': instance.records,
      'current': instance.current,
      'pages': instance.pages,
      'size': instance.size,
      'total': instance.total,
    };
