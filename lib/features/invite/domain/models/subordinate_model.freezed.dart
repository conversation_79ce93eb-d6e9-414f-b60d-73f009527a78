// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'subordinate_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SubordinateModel _$SubordinateModelFromJson(Map<String, dynamic> json) {
  return _SubordinateModel.fromJson(json);
}

/// @nodoc
mixin _$SubordinateModel {
  SubordinateDetailModel? get detail => throw _privateConstructorUsedError;
  String? get displayName => throw _privateConstructorUsedError;
  bool? get isValid => throw _privateConstructorUsedError;
  bool? get status => throw _privateConstructorUsedError;
  double? get totalRebateFromSubordinate => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;
  int? get userId => throw _privateConstructorUsedError;

  /// Serializes this SubordinateModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SubordinateModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SubordinateModelCopyWith<SubordinateModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubordinateModelCopyWith<$Res> {
  factory $SubordinateModelCopyWith(
          SubordinateModel value, $Res Function(SubordinateModel) then) =
      _$SubordinateModelCopyWithImpl<$Res, SubordinateModel>;
  @useResult
  $Res call(
      {SubordinateDetailModel? detail,
      String? displayName,
      bool? isValid,
      bool? status,
      double? totalRebateFromSubordinate,
      int? type,
      int? userId});

  $SubordinateDetailModelCopyWith<$Res>? get detail;
}

/// @nodoc
class _$SubordinateModelCopyWithImpl<$Res, $Val extends SubordinateModel>
    implements $SubordinateModelCopyWith<$Res> {
  _$SubordinateModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SubordinateModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? detail = freezed,
    Object? displayName = freezed,
    Object? isValid = freezed,
    Object? status = freezed,
    Object? totalRebateFromSubordinate = freezed,
    Object? type = freezed,
    Object? userId = freezed,
  }) {
    return _then(_value.copyWith(
      detail: freezed == detail
          ? _value.detail
          : detail // ignore: cast_nullable_to_non_nullable
              as SubordinateDetailModel?,
      displayName: freezed == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String?,
      isValid: freezed == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as bool?,
      totalRebateFromSubordinate: freezed == totalRebateFromSubordinate
          ? _value.totalRebateFromSubordinate
          : totalRebateFromSubordinate // ignore: cast_nullable_to_non_nullable
              as double?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  /// Create a copy of SubordinateModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SubordinateDetailModelCopyWith<$Res>? get detail {
    if (_value.detail == null) {
      return null;
    }

    return $SubordinateDetailModelCopyWith<$Res>(_value.detail!, (value) {
      return _then(_value.copyWith(detail: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SubordinateModelImplCopyWith<$Res>
    implements $SubordinateModelCopyWith<$Res> {
  factory _$$SubordinateModelImplCopyWith(_$SubordinateModelImpl value,
          $Res Function(_$SubordinateModelImpl) then) =
      __$$SubordinateModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {SubordinateDetailModel? detail,
      String? displayName,
      bool? isValid,
      bool? status,
      double? totalRebateFromSubordinate,
      int? type,
      int? userId});

  @override
  $SubordinateDetailModelCopyWith<$Res>? get detail;
}

/// @nodoc
class __$$SubordinateModelImplCopyWithImpl<$Res>
    extends _$SubordinateModelCopyWithImpl<$Res, _$SubordinateModelImpl>
    implements _$$SubordinateModelImplCopyWith<$Res> {
  __$$SubordinateModelImplCopyWithImpl(_$SubordinateModelImpl _value,
      $Res Function(_$SubordinateModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SubordinateModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? detail = freezed,
    Object? displayName = freezed,
    Object? isValid = freezed,
    Object? status = freezed,
    Object? totalRebateFromSubordinate = freezed,
    Object? type = freezed,
    Object? userId = freezed,
  }) {
    return _then(_$SubordinateModelImpl(
      detail: freezed == detail
          ? _value.detail
          : detail // ignore: cast_nullable_to_non_nullable
              as SubordinateDetailModel?,
      displayName: freezed == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String?,
      isValid: freezed == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as bool?,
      totalRebateFromSubordinate: freezed == totalRebateFromSubordinate
          ? _value.totalRebateFromSubordinate
          : totalRebateFromSubordinate // ignore: cast_nullable_to_non_nullable
              as double?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SubordinateModelImpl implements _SubordinateModel {
  const _$SubordinateModelImpl(
      {this.detail,
      this.displayName,
      this.isValid,
      this.status,
      this.totalRebateFromSubordinate,
      this.type,
      this.userId});

  factory _$SubordinateModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SubordinateModelImplFromJson(json);

  @override
  final SubordinateDetailModel? detail;
  @override
  final String? displayName;
  @override
  final bool? isValid;
  @override
  final bool? status;
  @override
  final double? totalRebateFromSubordinate;
  @override
  final int? type;
  @override
  final int? userId;

  @override
  String toString() {
    return 'SubordinateModel(detail: $detail, displayName: $displayName, isValid: $isValid, status: $status, totalRebateFromSubordinate: $totalRebateFromSubordinate, type: $type, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubordinateModelImpl &&
            (identical(other.detail, detail) || other.detail == detail) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.isValid, isValid) || other.isValid == isValid) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.totalRebateFromSubordinate,
                    totalRebateFromSubordinate) ||
                other.totalRebateFromSubordinate ==
                    totalRebateFromSubordinate) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, detail, displayName, isValid,
      status, totalRebateFromSubordinate, type, userId);

  /// Create a copy of SubordinateModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubordinateModelImplCopyWith<_$SubordinateModelImpl> get copyWith =>
      __$$SubordinateModelImplCopyWithImpl<_$SubordinateModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SubordinateModelImplToJson(
      this,
    );
  }
}

abstract class _SubordinateModel implements SubordinateModel {
  const factory _SubordinateModel(
      {final SubordinateDetailModel? detail,
      final String? displayName,
      final bool? isValid,
      final bool? status,
      final double? totalRebateFromSubordinate,
      final int? type,
      final int? userId}) = _$SubordinateModelImpl;

  factory _SubordinateModel.fromJson(Map<String, dynamic> json) =
      _$SubordinateModelImpl.fromJson;

  @override
  SubordinateDetailModel? get detail;
  @override
  String? get displayName;
  @override
  bool? get isValid;
  @override
  bool? get status;
  @override
  double? get totalRebateFromSubordinate;
  @override
  int? get type;
  @override
  int? get userId;

  /// Create a copy of SubordinateModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubordinateModelImplCopyWith<_$SubordinateModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SubordinateDetailModel _$SubordinateDetailModelFromJson(
    Map<String, dynamic> json) {
  return _SubordinateDetailModel.fromJson(json);
}

/// @nodoc
mixin _$SubordinateDetailModel {
  double? get balance => throw _privateConstructorUsedError;
  double? get frozenAmount => throw _privateConstructorUsedError;
  double? get margin => throw _privateConstructorUsedError;
  String? get maskedIdCard => throw _privateConstructorUsedError;
  String? get maskedMobile => throw _privateConstructorUsedError;
  String? get memberName => throw _privateConstructorUsedError;

  /// Serializes this SubordinateDetailModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SubordinateDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SubordinateDetailModelCopyWith<SubordinateDetailModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubordinateDetailModelCopyWith<$Res> {
  factory $SubordinateDetailModelCopyWith(SubordinateDetailModel value,
          $Res Function(SubordinateDetailModel) then) =
      _$SubordinateDetailModelCopyWithImpl<$Res, SubordinateDetailModel>;
  @useResult
  $Res call(
      {double? balance,
      double? frozenAmount,
      double? margin,
      String? maskedIdCard,
      String? maskedMobile,
      String? memberName});
}

/// @nodoc
class _$SubordinateDetailModelCopyWithImpl<$Res,
        $Val extends SubordinateDetailModel>
    implements $SubordinateDetailModelCopyWith<$Res> {
  _$SubordinateDetailModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SubordinateDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? balance = freezed,
    Object? frozenAmount = freezed,
    Object? margin = freezed,
    Object? maskedIdCard = freezed,
    Object? maskedMobile = freezed,
    Object? memberName = freezed,
  }) {
    return _then(_value.copyWith(
      balance: freezed == balance
          ? _value.balance
          : balance // ignore: cast_nullable_to_non_nullable
              as double?,
      frozenAmount: freezed == frozenAmount
          ? _value.frozenAmount
          : frozenAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      margin: freezed == margin
          ? _value.margin
          : margin // ignore: cast_nullable_to_non_nullable
              as double?,
      maskedIdCard: freezed == maskedIdCard
          ? _value.maskedIdCard
          : maskedIdCard // ignore: cast_nullable_to_non_nullable
              as String?,
      maskedMobile: freezed == maskedMobile
          ? _value.maskedMobile
          : maskedMobile // ignore: cast_nullable_to_non_nullable
              as String?,
      memberName: freezed == memberName
          ? _value.memberName
          : memberName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SubordinateDetailModelImplCopyWith<$Res>
    implements $SubordinateDetailModelCopyWith<$Res> {
  factory _$$SubordinateDetailModelImplCopyWith(
          _$SubordinateDetailModelImpl value,
          $Res Function(_$SubordinateDetailModelImpl) then) =
      __$$SubordinateDetailModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double? balance,
      double? frozenAmount,
      double? margin,
      String? maskedIdCard,
      String? maskedMobile,
      String? memberName});
}

/// @nodoc
class __$$SubordinateDetailModelImplCopyWithImpl<$Res>
    extends _$SubordinateDetailModelCopyWithImpl<$Res,
        _$SubordinateDetailModelImpl>
    implements _$$SubordinateDetailModelImplCopyWith<$Res> {
  __$$SubordinateDetailModelImplCopyWithImpl(
      _$SubordinateDetailModelImpl _value,
      $Res Function(_$SubordinateDetailModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SubordinateDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? balance = freezed,
    Object? frozenAmount = freezed,
    Object? margin = freezed,
    Object? maskedIdCard = freezed,
    Object? maskedMobile = freezed,
    Object? memberName = freezed,
  }) {
    return _then(_$SubordinateDetailModelImpl(
      balance: freezed == balance
          ? _value.balance
          : balance // ignore: cast_nullable_to_non_nullable
              as double?,
      frozenAmount: freezed == frozenAmount
          ? _value.frozenAmount
          : frozenAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      margin: freezed == margin
          ? _value.margin
          : margin // ignore: cast_nullable_to_non_nullable
              as double?,
      maskedIdCard: freezed == maskedIdCard
          ? _value.maskedIdCard
          : maskedIdCard // ignore: cast_nullable_to_non_nullable
              as String?,
      maskedMobile: freezed == maskedMobile
          ? _value.maskedMobile
          : maskedMobile // ignore: cast_nullable_to_non_nullable
              as String?,
      memberName: freezed == memberName
          ? _value.memberName
          : memberName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SubordinateDetailModelImpl implements _SubordinateDetailModel {
  const _$SubordinateDetailModelImpl(
      {this.balance,
      this.frozenAmount,
      this.margin,
      this.maskedIdCard,
      this.maskedMobile,
      this.memberName});

  factory _$SubordinateDetailModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SubordinateDetailModelImplFromJson(json);

  @override
  final double? balance;
  @override
  final double? frozenAmount;
  @override
  final double? margin;
  @override
  final String? maskedIdCard;
  @override
  final String? maskedMobile;
  @override
  final String? memberName;

  @override
  String toString() {
    return 'SubordinateDetailModel(balance: $balance, frozenAmount: $frozenAmount, margin: $margin, maskedIdCard: $maskedIdCard, maskedMobile: $maskedMobile, memberName: $memberName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubordinateDetailModelImpl &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.frozenAmount, frozenAmount) ||
                other.frozenAmount == frozenAmount) &&
            (identical(other.margin, margin) || other.margin == margin) &&
            (identical(other.maskedIdCard, maskedIdCard) ||
                other.maskedIdCard == maskedIdCard) &&
            (identical(other.maskedMobile, maskedMobile) ||
                other.maskedMobile == maskedMobile) &&
            (identical(other.memberName, memberName) ||
                other.memberName == memberName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, balance, frozenAmount, margin,
      maskedIdCard, maskedMobile, memberName);

  /// Create a copy of SubordinateDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubordinateDetailModelImplCopyWith<_$SubordinateDetailModelImpl>
      get copyWith => __$$SubordinateDetailModelImplCopyWithImpl<
          _$SubordinateDetailModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SubordinateDetailModelImplToJson(
      this,
    );
  }
}

abstract class _SubordinateDetailModel implements SubordinateDetailModel {
  const factory _SubordinateDetailModel(
      {final double? balance,
      final double? frozenAmount,
      final double? margin,
      final String? maskedIdCard,
      final String? maskedMobile,
      final String? memberName}) = _$SubordinateDetailModelImpl;

  factory _SubordinateDetailModel.fromJson(Map<String, dynamic> json) =
      _$SubordinateDetailModelImpl.fromJson;

  @override
  double? get balance;
  @override
  double? get frozenAmount;
  @override
  double? get margin;
  @override
  String? get maskedIdCard;
  @override
  String? get maskedMobile;
  @override
  String? get memberName;

  /// Create a copy of SubordinateDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubordinateDetailModelImplCopyWith<_$SubordinateDetailModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

SubordinatePaginatedResponse _$SubordinatePaginatedResponseFromJson(
    Map<String, dynamic> json) {
  return _SubordinatePaginatedResponse.fromJson(json);
}

/// @nodoc
mixin _$SubordinatePaginatedResponse {
  List<SubordinateModel> get records => throw _privateConstructorUsedError;
  int get current => throw _privateConstructorUsedError;
  int get pages => throw _privateConstructorUsedError;
  int get size => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;

  /// Serializes this SubordinatePaginatedResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SubordinatePaginatedResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SubordinatePaginatedResponseCopyWith<SubordinatePaginatedResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubordinatePaginatedResponseCopyWith<$Res> {
  factory $SubordinatePaginatedResponseCopyWith(
          SubordinatePaginatedResponse value,
          $Res Function(SubordinatePaginatedResponse) then) =
      _$SubordinatePaginatedResponseCopyWithImpl<$Res,
          SubordinatePaginatedResponse>;
  @useResult
  $Res call(
      {List<SubordinateModel> records,
      int current,
      int pages,
      int size,
      int total});
}

/// @nodoc
class _$SubordinatePaginatedResponseCopyWithImpl<$Res,
        $Val extends SubordinatePaginatedResponse>
    implements $SubordinatePaginatedResponseCopyWith<$Res> {
  _$SubordinatePaginatedResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SubordinatePaginatedResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? records = null,
    Object? current = null,
    Object? pages = null,
    Object? size = null,
    Object? total = null,
  }) {
    return _then(_value.copyWith(
      records: null == records
          ? _value.records
          : records // ignore: cast_nullable_to_non_nullable
              as List<SubordinateModel>,
      current: null == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int,
      pages: null == pages
          ? _value.pages
          : pages // ignore: cast_nullable_to_non_nullable
              as int,
      size: null == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SubordinatePaginatedResponseImplCopyWith<$Res>
    implements $SubordinatePaginatedResponseCopyWith<$Res> {
  factory _$$SubordinatePaginatedResponseImplCopyWith(
          _$SubordinatePaginatedResponseImpl value,
          $Res Function(_$SubordinatePaginatedResponseImpl) then) =
      __$$SubordinatePaginatedResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<SubordinateModel> records,
      int current,
      int pages,
      int size,
      int total});
}

/// @nodoc
class __$$SubordinatePaginatedResponseImplCopyWithImpl<$Res>
    extends _$SubordinatePaginatedResponseCopyWithImpl<$Res,
        _$SubordinatePaginatedResponseImpl>
    implements _$$SubordinatePaginatedResponseImplCopyWith<$Res> {
  __$$SubordinatePaginatedResponseImplCopyWithImpl(
      _$SubordinatePaginatedResponseImpl _value,
      $Res Function(_$SubordinatePaginatedResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of SubordinatePaginatedResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? records = null,
    Object? current = null,
    Object? pages = null,
    Object? size = null,
    Object? total = null,
  }) {
    return _then(_$SubordinatePaginatedResponseImpl(
      records: null == records
          ? _value._records
          : records // ignore: cast_nullable_to_non_nullable
              as List<SubordinateModel>,
      current: null == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int,
      pages: null == pages
          ? _value.pages
          : pages // ignore: cast_nullable_to_non_nullable
              as int,
      size: null == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SubordinatePaginatedResponseImpl
    implements _SubordinatePaginatedResponse {
  const _$SubordinatePaginatedResponseImpl(
      {required final List<SubordinateModel> records,
      required this.current,
      required this.pages,
      required this.size,
      required this.total})
      : _records = records;

  factory _$SubordinatePaginatedResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$SubordinatePaginatedResponseImplFromJson(json);

  final List<SubordinateModel> _records;
  @override
  List<SubordinateModel> get records {
    if (_records is EqualUnmodifiableListView) return _records;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_records);
  }

  @override
  final int current;
  @override
  final int pages;
  @override
  final int size;
  @override
  final int total;

  @override
  String toString() {
    return 'SubordinatePaginatedResponse(records: $records, current: $current, pages: $pages, size: $size, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubordinatePaginatedResponseImpl &&
            const DeepCollectionEquality().equals(other._records, _records) &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.pages, pages) || other.pages == pages) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_records),
      current,
      pages,
      size,
      total);

  /// Create a copy of SubordinatePaginatedResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubordinatePaginatedResponseImplCopyWith<
          _$SubordinatePaginatedResponseImpl>
      get copyWith => __$$SubordinatePaginatedResponseImplCopyWithImpl<
          _$SubordinatePaginatedResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SubordinatePaginatedResponseImplToJson(
      this,
    );
  }
}

abstract class _SubordinatePaginatedResponse
    implements SubordinatePaginatedResponse {
  const factory _SubordinatePaginatedResponse(
      {required final List<SubordinateModel> records,
      required final int current,
      required final int pages,
      required final int size,
      required final int total}) = _$SubordinatePaginatedResponseImpl;

  factory _SubordinatePaginatedResponse.fromJson(Map<String, dynamic> json) =
      _$SubordinatePaginatedResponseImpl.fromJson;

  @override
  List<SubordinateModel> get records;
  @override
  int get current;
  @override
  int get pages;
  @override
  int get size;
  @override
  int get total;

  /// Create a copy of SubordinatePaginatedResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubordinatePaginatedResponseImplCopyWith<
          _$SubordinatePaginatedResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
