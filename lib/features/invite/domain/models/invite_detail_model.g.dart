// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invite_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$InviteDetailModelImpl _$$InviteDetailModelImplFromJson(
        Map<String, dynamic> json) =>
    _$InviteDetailModelImpl(
      currentVipLevel: (json['currentVipLevel'] as num?)?.toInt(),
      interestRebateRate: (json['interestRebateRate'] as num?)?.toDouble(),
      subordinateList: (json['subordinateList'] as List<dynamic>?)
          ?.map((e) => SubordinateModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalRebate: (json['totalRebate'] as num?)?.toDouble(),
      totalSatisfiedInviteCount:
          (json['totalSatisfiedInviteCount'] as num?)?.toInt(),
      totalSubordinates: (json['totalSubordinates'] as num?)?.toInt(),
      tradingRebateRate: (json['tradingRebateRate'] as num?)?.toDouble(),
      vipLevelTableList: (json['vipLevelTableList'] as List<dynamic>?)
          ?.map((e) => VipLevelTableModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      inviteCode: json['inviteCode'] as String?,
      customInviteLink: json['customInviteLink'] as String?,
    );

Map<String, dynamic> _$$InviteDetailModelImplToJson(
        _$InviteDetailModelImpl instance) =>
    <String, dynamic>{
      'currentVipLevel': instance.currentVipLevel,
      'interestRebateRate': instance.interestRebateRate,
      'subordinateList': instance.subordinateList,
      'totalRebate': instance.totalRebate,
      'totalSatisfiedInviteCount': instance.totalSatisfiedInviteCount,
      'totalSubordinates': instance.totalSubordinates,
      'tradingRebateRate': instance.tradingRebateRate,
      'vipLevelTableList': instance.vipLevelTableList,
      'inviteCode': instance.inviteCode,
      'customInviteLink': instance.customInviteLink,
    };
