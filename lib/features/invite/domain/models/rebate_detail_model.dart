import 'package:freezed_annotation/freezed_annotation.dart';

part 'rebate_detail_model.freezed.dart';
part 'rebate_detail_model.g.dart';

@freezed
class RebateDetailModel with _$RebateDetailModel {
  const factory RebateDetailModel({
    int? contractId,
    String? contractNumber,
    int? contractType,
    String? createTime,
    int? id,
    String? marketType,
    String? mobile,
    int? multiple,
    String? name,
    int? orderId,
    int? periodType,
    double? rebateAmount,
    int? rebateType,
    double? refundAmount,
    int? userId,
  }) = _RebateDetailModel;

  factory RebateDetailModel.fromJson(Map<String, dynamic> json) => _$RebateDetailModelFromJson(json);
}
