// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rebate_detail_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RebateDetailModel _$RebateDetailModelFromJson(Map<String, dynamic> json) {
  return _RebateDetailModel.fromJson(json);
}

/// @nodoc
mixin _$RebateDetailModel {
  int? get contractId => throw _privateConstructorUsedError;
  String? get contractNumber => throw _privateConstructorUsedError;
  int? get contractType => throw _privateConstructorUsedError;
  String? get createTime => throw _privateConstructorUsedError;
  int? get id => throw _privateConstructorUsedError;
  String? get marketType => throw _privateConstructorUsedError;
  String? get mobile => throw _privateConstructorUsedError;
  int? get multiple => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  int? get orderId => throw _privateConstructorUsedError;
  int? get periodType => throw _privateConstructorUsedError;
  double? get rebateAmount => throw _privateConstructorUsedError;
  int? get rebateType => throw _privateConstructorUsedError;
  double? get refundAmount => throw _privateConstructorUsedError;
  int? get userId => throw _privateConstructorUsedError;

  /// Serializes this RebateDetailModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RebateDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RebateDetailModelCopyWith<RebateDetailModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RebateDetailModelCopyWith<$Res> {
  factory $RebateDetailModelCopyWith(
          RebateDetailModel value, $Res Function(RebateDetailModel) then) =
      _$RebateDetailModelCopyWithImpl<$Res, RebateDetailModel>;
  @useResult
  $Res call(
      {int? contractId,
      String? contractNumber,
      int? contractType,
      String? createTime,
      int? id,
      String? marketType,
      String? mobile,
      int? multiple,
      String? name,
      int? orderId,
      int? periodType,
      double? rebateAmount,
      int? rebateType,
      double? refundAmount,
      int? userId});
}

/// @nodoc
class _$RebateDetailModelCopyWithImpl<$Res, $Val extends RebateDetailModel>
    implements $RebateDetailModelCopyWith<$Res> {
  _$RebateDetailModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RebateDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractId = freezed,
    Object? contractNumber = freezed,
    Object? contractType = freezed,
    Object? createTime = freezed,
    Object? id = freezed,
    Object? marketType = freezed,
    Object? mobile = freezed,
    Object? multiple = freezed,
    Object? name = freezed,
    Object? orderId = freezed,
    Object? periodType = freezed,
    Object? rebateAmount = freezed,
    Object? rebateType = freezed,
    Object? refundAmount = freezed,
    Object? userId = freezed,
  }) {
    return _then(_value.copyWith(
      contractId: freezed == contractId
          ? _value.contractId
          : contractId // ignore: cast_nullable_to_non_nullable
              as int?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      contractType: freezed == contractType
          ? _value.contractType
          : contractType // ignore: cast_nullable_to_non_nullable
              as int?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      marketType: freezed == marketType
          ? _value.marketType
          : marketType // ignore: cast_nullable_to_non_nullable
              as String?,
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
      multiple: freezed == multiple
          ? _value.multiple
          : multiple // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as int?,
      periodType: freezed == periodType
          ? _value.periodType
          : periodType // ignore: cast_nullable_to_non_nullable
              as int?,
      rebateAmount: freezed == rebateAmount
          ? _value.rebateAmount
          : rebateAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      rebateType: freezed == rebateType
          ? _value.rebateType
          : rebateType // ignore: cast_nullable_to_non_nullable
              as int?,
      refundAmount: freezed == refundAmount
          ? _value.refundAmount
          : refundAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RebateDetailModelImplCopyWith<$Res>
    implements $RebateDetailModelCopyWith<$Res> {
  factory _$$RebateDetailModelImplCopyWith(_$RebateDetailModelImpl value,
          $Res Function(_$RebateDetailModelImpl) then) =
      __$$RebateDetailModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? contractId,
      String? contractNumber,
      int? contractType,
      String? createTime,
      int? id,
      String? marketType,
      String? mobile,
      int? multiple,
      String? name,
      int? orderId,
      int? periodType,
      double? rebateAmount,
      int? rebateType,
      double? refundAmount,
      int? userId});
}

/// @nodoc
class __$$RebateDetailModelImplCopyWithImpl<$Res>
    extends _$RebateDetailModelCopyWithImpl<$Res, _$RebateDetailModelImpl>
    implements _$$RebateDetailModelImplCopyWith<$Res> {
  __$$RebateDetailModelImplCopyWithImpl(_$RebateDetailModelImpl _value,
      $Res Function(_$RebateDetailModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of RebateDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractId = freezed,
    Object? contractNumber = freezed,
    Object? contractType = freezed,
    Object? createTime = freezed,
    Object? id = freezed,
    Object? marketType = freezed,
    Object? mobile = freezed,
    Object? multiple = freezed,
    Object? name = freezed,
    Object? orderId = freezed,
    Object? periodType = freezed,
    Object? rebateAmount = freezed,
    Object? rebateType = freezed,
    Object? refundAmount = freezed,
    Object? userId = freezed,
  }) {
    return _then(_$RebateDetailModelImpl(
      contractId: freezed == contractId
          ? _value.contractId
          : contractId // ignore: cast_nullable_to_non_nullable
              as int?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      contractType: freezed == contractType
          ? _value.contractType
          : contractType // ignore: cast_nullable_to_non_nullable
              as int?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      marketType: freezed == marketType
          ? _value.marketType
          : marketType // ignore: cast_nullable_to_non_nullable
              as String?,
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
      multiple: freezed == multiple
          ? _value.multiple
          : multiple // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as int?,
      periodType: freezed == periodType
          ? _value.periodType
          : periodType // ignore: cast_nullable_to_non_nullable
              as int?,
      rebateAmount: freezed == rebateAmount
          ? _value.rebateAmount
          : rebateAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      rebateType: freezed == rebateType
          ? _value.rebateType
          : rebateType // ignore: cast_nullable_to_non_nullable
              as int?,
      refundAmount: freezed == refundAmount
          ? _value.refundAmount
          : refundAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RebateDetailModelImpl implements _RebateDetailModel {
  const _$RebateDetailModelImpl(
      {this.contractId,
      this.contractNumber,
      this.contractType,
      this.createTime,
      this.id,
      this.marketType,
      this.mobile,
      this.multiple,
      this.name,
      this.orderId,
      this.periodType,
      this.rebateAmount,
      this.rebateType,
      this.refundAmount,
      this.userId});

  factory _$RebateDetailModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$RebateDetailModelImplFromJson(json);

  @override
  final int? contractId;
  @override
  final String? contractNumber;
  @override
  final int? contractType;
  @override
  final String? createTime;
  @override
  final int? id;
  @override
  final String? marketType;
  @override
  final String? mobile;
  @override
  final int? multiple;
  @override
  final String? name;
  @override
  final int? orderId;
  @override
  final int? periodType;
  @override
  final double? rebateAmount;
  @override
  final int? rebateType;
  @override
  final double? refundAmount;
  @override
  final int? userId;

  @override
  String toString() {
    return 'RebateDetailModel(contractId: $contractId, contractNumber: $contractNumber, contractType: $contractType, createTime: $createTime, id: $id, marketType: $marketType, mobile: $mobile, multiple: $multiple, name: $name, orderId: $orderId, periodType: $periodType, rebateAmount: $rebateAmount, rebateType: $rebateType, refundAmount: $refundAmount, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RebateDetailModelImpl &&
            (identical(other.contractId, contractId) ||
                other.contractId == contractId) &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber) &&
            (identical(other.contractType, contractType) ||
                other.contractType == contractType) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.marketType, marketType) ||
                other.marketType == marketType) &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            (identical(other.multiple, multiple) ||
                other.multiple == multiple) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.periodType, periodType) ||
                other.periodType == periodType) &&
            (identical(other.rebateAmount, rebateAmount) ||
                other.rebateAmount == rebateAmount) &&
            (identical(other.rebateType, rebateType) ||
                other.rebateType == rebateType) &&
            (identical(other.refundAmount, refundAmount) ||
                other.refundAmount == refundAmount) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      contractId,
      contractNumber,
      contractType,
      createTime,
      id,
      marketType,
      mobile,
      multiple,
      name,
      orderId,
      periodType,
      rebateAmount,
      rebateType,
      refundAmount,
      userId);

  /// Create a copy of RebateDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RebateDetailModelImplCopyWith<_$RebateDetailModelImpl> get copyWith =>
      __$$RebateDetailModelImplCopyWithImpl<_$RebateDetailModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RebateDetailModelImplToJson(
      this,
    );
  }
}

abstract class _RebateDetailModel implements RebateDetailModel {
  const factory _RebateDetailModel(
      {final int? contractId,
      final String? contractNumber,
      final int? contractType,
      final String? createTime,
      final int? id,
      final String? marketType,
      final String? mobile,
      final int? multiple,
      final String? name,
      final int? orderId,
      final int? periodType,
      final double? rebateAmount,
      final int? rebateType,
      final double? refundAmount,
      final int? userId}) = _$RebateDetailModelImpl;

  factory _RebateDetailModel.fromJson(Map<String, dynamic> json) =
      _$RebateDetailModelImpl.fromJson;

  @override
  int? get contractId;
  @override
  String? get contractNumber;
  @override
  int? get contractType;
  @override
  String? get createTime;
  @override
  int? get id;
  @override
  String? get marketType;
  @override
  String? get mobile;
  @override
  int? get multiple;
  @override
  String? get name;
  @override
  int? get orderId;
  @override
  int? get periodType;
  @override
  double? get rebateAmount;
  @override
  int? get rebateType;
  @override
  double? get refundAmount;
  @override
  int? get userId;

  /// Create a copy of RebateDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RebateDetailModelImplCopyWith<_$RebateDetailModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
