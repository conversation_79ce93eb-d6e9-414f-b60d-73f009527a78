import 'package:freezed_annotation/freezed_annotation.dart';
import 'subordinate_model.dart';
import 'vip_level_table_model.dart';

part 'invite_detail_model.freezed.dart';
part 'invite_detail_model.g.dart';

@freezed
class InviteDetailModel with _$InviteDetailModel {
  const factory InviteDetailModel({
    int? currentVipLevel,
    double? interestRebateRate,
    List<SubordinateModel>? subordinateList,
    double? totalRebate,
    double? totalRechargeAmount,
    int? totalSatisfiedInviteCount,
    int? totalSubordinates,
    double? tradingRebateRate,
    List<VipLevelTableModel>? vipLevelTableList,
    String? inviteCode,
    String? customInviteLink,
  }) = _InviteDetailModel;

  factory InviteDetailModel.fromJson(Map<String, dynamic> json) => _$InviteDetailModelFromJson(json);
}
