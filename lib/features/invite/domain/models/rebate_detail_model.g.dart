// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rebate_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RebateDetailModelImpl _$$RebateDetailModelImplFromJson(
        Map<String, dynamic> json) =>
    _$RebateDetailModelImpl(
      contractId: (json['contractId'] as num?)?.toInt(),
      contractNumber: json['contractNumber'] as String?,
      contractType: (json['contractType'] as num?)?.toInt(),
      createTime: json['createTime'] as String?,
      id: (json['id'] as num?)?.toInt(),
      marketType: json['marketType'] as String?,
      mobile: json['mobile'] as String?,
      multiple: (json['multiple'] as num?)?.toInt(),
      name: json['name'] as String?,
      orderId: (json['orderId'] as num?)?.toInt(),
      periodType: (json['periodType'] as num?)?.toInt(),
      rebateAmount: (json['rebateAmount'] as num?)?.toDouble(),
      rebateType: (json['rebateType'] as num?)?.toInt(),
      refundAmount: (json['refundAmount'] as num?)?.toDouble(),
      userId: (json['userId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$RebateDetailModelImplToJson(
        _$RebateDetailModelImpl instance) =>
    <String, dynamic>{
      'contractId': instance.contractId,
      'contractNumber': instance.contractNumber,
      'contractType': instance.contractType,
      'createTime': instance.createTime,
      'id': instance.id,
      'marketType': instance.marketType,
      'mobile': instance.mobile,
      'multiple': instance.multiple,
      'name': instance.name,
      'orderId': instance.orderId,
      'periodType': instance.periodType,
      'rebateAmount': instance.rebateAmount,
      'rebateType': instance.rebateType,
      'refundAmount': instance.refundAmount,
      'userId': instance.userId,
    };
