import 'package:injectable/injectable.dart';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/api/network/models/result.dart';
import '../../../../core/services/http/http.dart';
import '../models/rebate_detail_model.dart';

import '../repository/rebate_repository.dart';

@Injectable(as: RebateRepository)
class RebateService implements RebateRepository {
  @override
  Future<ResponseResult<RebatePaginatedResponse>> getRebateDetails({
    int page = 1,
    int pageSize = 20,
    String? searchQuery,
  }) async {
    final queryParameters = <String, dynamic>{
      'pageNumber': page,
      'pageSize': pageSize,
      if (searchQuery != null && searchQuery.isNotEmpty) 'keyword': searchQuery,
    };

    final response = await Http().request(
      ApiEndpoints.rebateCommission,
      method: HttpMethod.get,
      queryParameters: queryParameters,
      needSignIn: true,
    );

    if (response.isSuccess && response.data != null) {
      // Handle paginated response structure
      Map<String, dynamic> responseData;
      if (response.data is Map<String, dynamic>) {
        responseData = response.data as Map<String, dynamic>;
      } else {
        return ResponseResult(
          error: 'Invalid response format',
        );
      }

      final List<dynamic> records = responseData['records'] ?? [];
      final rebateDetails = records.map((json) => RebateDetailModel.fromJson(json as Map<String, dynamic>)).toList();

      final paginatedResponse = RebatePaginatedResponse(
        records: rebateDetails,
        current: responseData['current'] ?? 0,
        pages: responseData['pages'] ?? 0,
        size: responseData['size'] ?? 0,
        total: responseData['total'] ?? 0,
      );

      return ResponseResult(
        data: paginatedResponse,
      );
    } else {
      return ResponseResult(
        error: response.msg ?? 'Failed to fetch rebate details',
      );
    }
  }
}
