import 'package:injectable/injectable.dart';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/api/network/models/result.dart';
import '../../../../core/services/http/http.dart';
import '../models/subordinate_model.dart';
import '../repository/subordinate_repository.dart';

@Injectable(as: SubordinateRepository)
class SubordinateService implements SubordinateRepository {
  @override
  Future<ResponseResult<SubordinatePaginatedResponse>> getSubordinateList({
    int page = 1,
    int pageSize = 20,
    String? searchQuery,
  }) async {
    final queryParameters = <String, dynamic>{
      'pageNumber': page,
      'pageSize': pageSize,
      if (searchQuery != null && searchQuery.isNotEmpty) 'keyword': searchQuery,
    };

    final response = await Http().request(
      ApiEndpoints.subordinateList,
      method: HttpMethod.get,
      queryParameters: queryParameters,
      needSignIn: true,
    );

    if (response.isSuccess && response.data != null) {
      // Handle paginated response structure
      Map<String, dynamic> responseData;
      if (response.data is Map<String, dynamic>) {
        responseData = response.data as Map<String, dynamic>;
      } else {
        return ResponseResult(
          error: 'Invalid response format',
        );
      }

      final List<dynamic> records = responseData['records'] ?? [];
      final subordinates = records.map((json) => SubordinateModel.fromJson(json as Map<String, dynamic>)).toList();

      final paginatedResponse = SubordinatePaginatedResponse(
        records: subordinates,
        current: responseData['current'] ?? 0,
        pages: responseData['pages'] ?? 0,
        size: responseData['size'] ?? 0,
        total: responseData['total'] ?? 0,
      );

      return ResponseResult(
        data: paginatedResponse,
      );
    } else {
      return ResponseResult(
        error: response.msg ?? 'Failed to fetch subordinate list',
      );
    }
  }

  /// Get subordinate details
  /// This will be implemented when the API is ready
  static Future<SubordinateModel?> getSubordinateDetails(int userId) async {
    // TODO: Implement when API is ready
    return null;

    // Future implementation:
    // final response = await NetworkHelper.request(
    //   endpoint: '/api/subordinate/details/$userId',
    //   method: 'GET',
    // );
    //
    // if (response.isSuccess) {
    //   return SubordinateModel.fromJson(response.data);
    // }
    //
    // throw Exception(response.message ?? 'Failed to fetch subordinate details');
  }
}
