import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';

import 'package:injectable/injectable.dart';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/api/network/models/result.dart';
import '../../../../core/api/network/network.dart';
import '../models/invite_detail_model.dart';
import '../repository/invite_repository.dart';

@Injectable(as: InviteRepository)
class InviteService implements InviteRepository {
  @override
  Future<ResponseResult<InviteDetailModel>> getInviteDetails() async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.inviteRebateDetail,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 && response.data['code'] == 0) {
        return ResponseResult(
          data: InviteDetailModel.fromJson(response.data['data']),
        );
      } else {
        return ResponseResult(
          error: response.data['msg'] ?? 'error'.tr(),
        );
      }
    } on DioException catch (e) {
      return ResponseResult(
        error: e.message ?? 'error'.tr(),
      );
    } catch (e) {
      return ResponseResult(
        error: e.toString(),
      );
    }
  }

  @override
  Future<ResponseResult<String>> getCustomInviteLink() async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.getInviteLink,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 && response.data['code'] == 0) {
        return ResponseResult(
          data: response.data['data'] as String,
        );
      } else {
        return ResponseResult(
          error: response.data['msg'] ?? 'error'.tr(),
        );
      }
    } on DioException catch (e) {
      return ResponseResult(
        error: e.message ?? 'error'.tr(),
      );
    } catch (e) {
      return ResponseResult(
        error: e.toString(),
      );
    }
  }
}
