import '../../../../core/api/network/models/result.dart';
import '../models/rebate_detail_model.dart';

class RebatePaginatedResponse {
  final List<RebateDetailModel> records;
  final int current;
  final int pages;
  final int size;
  final int total;

  RebatePaginatedResponse({
    required this.records,
    required this.current,
    required this.pages,
    required this.size,
    required this.total,
  });
}

abstract class RebateRepository {
  Future<ResponseResult<RebatePaginatedResponse>> getRebateDetails({
    int page = 1,
    int pageSize = 20,
    String? searchQuery,
  });
}
