part of 'rebate_cubit.dart';

class RebateState extends Equatable {
  final DataStatus getRebateDetailsStatus;
  final List<RebateDetailModel> rebateDetails;
  final int currentPage;
  final int totalPages;
  final bool hasMore;
  final bool isLoadMore;
  final String? searchQuery;
  final String? error;

  const RebateState({
    this.getRebateDetailsStatus = DataStatus.idle,
    this.rebateDetails = const [],
    this.currentPage = 1,
    this.totalPages = 0,
    this.hasMore = true,
    this.isLoadMore = false,
    this.searchQuery,
    this.error,
  });

  @override
  List<Object?> get props => [
        getRebateDetailsStatus,
        rebateDetails,
        currentPage,
        totalPages,
        hasMore,
        isLoadMore,
        searchQuery,
        error,
      ];

  RebateState copyWith({
    DataStatus? getRebateDetailsStatus,
    List<RebateDetailModel>? rebateDetails,
    int? currentPage,
    int? totalPages,
    bool? hasMore,
    bool? isLoadMore,
    String? searchQuery,
    String? error,
  }) {
    return RebateState(
      getRebateDetailsStatus: getRebateDetailsStatus ?? this.getRebateDetailsStatus,
      rebateDetails: rebateDetails ?? this.rebateDetails,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      hasMore: hasMore ?? this.hasMore,
      isLoadMore: isLoadMore ?? this.isLoadMore,
      searchQuery: searchQuery ?? this.searchQuery,
      error: error ?? this.error,
    );
  }
}
