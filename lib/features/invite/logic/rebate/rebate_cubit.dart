import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/invite/domain/models/rebate_detail_model.dart';
import 'package:gp_stock_app/features/invite/domain/repository/rebate_repository.dart';
import 'package:gp_stock_app/features/invite/domain/services/rebate_service.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 'rebate_state.dart';

class RebateCubit extends Cubit<RebateState> {
  final RebateRepository _rebateRepository = RebateService();

  RebateCubit() : super(const RebateState());

  /// Get rebate details list
  Future<void> getRebateDetails({bool loadMore = false, String? searchQuery}) async {
    if (state.getRebateDetailsStatus == DataStatus.loading) return;

    final int pageNumber = loadMore ? (state.currentPage) + 1 : 1;

    if (!loadMore) {
      emit(state.copyWith(getRebateDetailsStatus: DataStatus.loading));
    }

    try {
      final result = await _rebateRepository.getRebateDetails(
        page: pageNumber,
        pageSize: 20,
        searchQuery: searchQuery,
      );

      if (result.data != null) {
        final paginatedData = result.data!;

        // Combine records based on loadMore
        final List<RebateDetailModel> currentList = loadMore
            ? (List<RebateDetailModel>.from(state.rebateDetails)..addAll(paginatedData.records))
            : paginatedData.records;

        emit(state.copyWith(
          getRebateDetailsStatus: DataStatus.success,
          rebateDetails: currentList,
          currentPage: paginatedData.current,
          totalPages: paginatedData.pages,
          hasMore: (currentList.length) < (paginatedData.total),
          searchQuery: searchQuery,
        ));
      } else {
        emit(state.copyWith(
          getRebateDetailsStatus: DataStatus.failed,
          error: result.error ?? 'Failed to fetch rebate details',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        getRebateDetailsStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  /// Search rebate details
  Future<void> searchRebateDetails(String query) async {
    await getRebateDetails(
      searchQuery: query.trim().isEmpty ? null : query.trim(),
    );
  }

  /// Load more rebate details
  Future<void> loadMore() async {
    await getRebateDetails(loadMore: true);
  }

  /// Refresh rebate details list
  Future<void> refresh() async {
    await getRebateDetails(searchQuery: state.searchQuery);
  }

  /// Clear search
  void clearSearch() {
    getRebateDetails();
  }
}
