import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/invite/domain/models/subordinate_model.dart';
import 'package:gp_stock_app/features/invite/domain/repository/subordinate_repository.dart';
import 'package:gp_stock_app/features/invite/domain/services/subordinate_service.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 'subordinate_state.dart';

class SubordinateCubit extends Cubit<SubordinateState> {
  final SubordinateRepository _subordinateRepository = SubordinateService();

  SubordinateCubit() : super(const SubordinateState());

  /// Get subordinate list
  Future<void> getSubordinateList({bool loadMore = false, String? searchQuery}) async {
    if (state.getSubordinateListStatus == DataStatus.loading) return;

    final int pageNumber = loadMore ? (state.currentPage) + 1 : 1;

    if (!loadMore) {
      emit(state.copyWith(getSubordinateListStatus: DataStatus.loading));
    }

    try {
      final result = await _subordinateRepository.getSubordinateList(
        page: pageNumber,
        pageSize: 20,
        searchQuery: searchQuery,
      );

      if (result.data != null) {
        final paginatedData = result.data!;

        // Combine records based on loadMore
        final List<SubordinateModel> currentList = loadMore
            ? (List<SubordinateModel>.from(state.subordinates)..addAll(paginatedData.records))
            : paginatedData.records;

        emit(state.copyWith(
          getSubordinateListStatus: DataStatus.success,
          subordinates: currentList,
          currentPage: paginatedData.current,
          totalPages: paginatedData.pages,
          hasMore: (currentList.length) < (paginatedData.total),
          searchQuery: searchQuery,
        ));
      } else {
        emit(state.copyWith(
          getSubordinateListStatus: DataStatus.failed,
          error: result.error ?? 'Failed to fetch subordinate list',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        getSubordinateListStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  /// Search subordinates
  Future<void> searchSubordinates(String query) async {
    await getSubordinateList(
      searchQuery: query.trim().isEmpty ? null : query.trim(),
    );
  }

  /// Refresh subordinate list
  Future<void> refresh() async {
    await getSubordinateList(
      searchQuery: state.searchQuery,
    );
  }
}
