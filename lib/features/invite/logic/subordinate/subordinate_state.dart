part of 'subordinate_cubit.dart';

class SubordinateState extends Equatable {
  final DataStatus getSubordinateListStatus;
  final List<SubordinateModel> subordinates;
  final int currentPage;
  final int totalPages;
  final bool hasMore;
  final String? searchQuery;
  final String? error;

  const SubordinateState({
    this.getSubordinateListStatus = DataStatus.idle,
    this.subordinates = const [],
    this.currentPage = 1,
    this.totalPages = 0,
    this.hasMore = false,
    this.searchQuery,
    this.error,
  });

  @override
  List<Object?> get props => [
        getSubordinateListStatus,
        subordinates,
        currentPage,
        totalPages,
        hasMore,
        searchQuery,
        error,
      ];

  SubordinateState copyWith({
    DataStatus? getSubordinateListStatus,
    List<SubordinateModel>? subordinates,
    int? currentPage,
    int? totalPages,
    bool? hasMore,
    String? searchQuery,
    String? error,
  }) {
    return SubordinateState(
      getSubordinateListStatus: getSubordinateListStatus ?? this.getSubordinateListStatus,
      subordinates: subordinates ?? this.subordinates,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      hasMore: hasMore ?? this.hasMore,
      searchQuery: searchQuery ?? this.searchQuery,
      error: error ?? this.error,
    );
  }
}
