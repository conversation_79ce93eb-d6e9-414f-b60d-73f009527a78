import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/features/account/domain/models/kline_option.dart';
import 'package:gp_stock_app/features/account/domain/models/order/order_response.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_all_info_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/logic/f_trade_k_line_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart' hide MainState;
import 'package:gp_stock_app/features/market/domain/models/stock_kline_data.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/features/market/logic/market/market_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/time_zone.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/route_arguments/trading_arguments.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';
import 'package:k_chart_plus/chart_style.dart';
import 'package:k_chart_plus/entity/k_line_entity.dart';
import 'package:k_chart_plus/k_chart_widget.dart';
import 'package:k_chart_plus/renderer/main_renderer.dart';
import 'package:k_chart_plus/utils/data_util.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

class TradeBottomsheetOrders extends StatelessWidget {
  const TradeBottomsheetOrders({
    super.key,
    required this.data,
    this.isTradeDetails = false,
    this.isTrading = true,
    this.contract,
  });

  final OrderRecord data;
  final bool isTradeDetails;
  final bool isTrading;

  /// Pass contract inorder to pass to trading center screen
  final ContractSummaryData? contract;
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 10.gh, horizontal: 12.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10.gh),
          topRight: Radius.circular(10.gh),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        spacing: 8,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            spacing: 5.gh,
            children: [
              SymbolChip(
                name: MarketSymbol.getMarketType(data.market ?? 'SZSE'),
                chipColor: context.theme.primaryColor,
              ),
              Text(data.symbolName ?? '', style: context.textTheme.regular),
              Text(
                '(${data.symbol ?? ''})',
                style: context.textTheme.regular.fs13.w300,
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: 12,
            children: [
              Expanded(
                child: AmountRow(
                    title: 'entrustmentPrice'.tr(),
                    value: '${data.tradePrice?.toStringAsFixed(2) ?? 0.00}',
                    fontSize: 13.gr),
              ),
              Expanded(
                child: AmountRow(
                  title: 'marketPrice'.tr(),
                  value: '${data.stockPrice?.toStringAsFixed(2) ?? 0.00}',
                  fontSize: 13.gr,
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: 12,
            children: [
              Expanded(
                child: AmountRow(
                    title: 'completed'.tr(), value: '${data.dealNum?.toStringAsFixed(2) ?? 0.00}', fontSize: 13.gr),
              ),
              Expanded(
                child: AmountRow(
                    title: 'total'.tr(), value: '${data.tradeNum?.toStringAsFixed(2) ?? 0.00}', fontSize: 13.gr),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: 12,
            children: [
              Expanded(
                child: AmountRow(
                  title: 'entrustDirection'.tr(),
                  value: positionActionStr(),
                  fontSize: 13.gr,
                  color:
                      TradeDirection.getColor(context, tradeDirection: TradeDirection.fromValue(data.direction ?? 0)),
                ),
              ),
              Expanded(
                child: AmountRow(
                  title: 'status'.tr(),
                  value: EntrustStatus.fromValueByValue(data.status ?? 0).label.tr(),
                  fontSize: 13.gr,
                  color: EntrustStatus.fromValueByValue(data.status ?? 0).color(context),
                ),
              ),
            ],
          ),
          AmountRow(
            title: 'entrustTime'.tr(),
            value: '${data.tradeTime}',
            fontSize: 13.gr,
          ),
          Row(
            spacing: 8,
            children: [
              Expanded(
                flex: 2,
                child: SizedBox(
                  height: 0.37.gsh,
                  child: AbsorbPointer(
                    child: data.isCnFTrade ? _buildFtradeKLineView() : _buildKLineView(),
                  ),
                ),
              ),
              Theme(
                data: Theme.of(context).copyWith(
                  textButtonTheme: TextButtonThemeData(
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(horizontal: 4),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      alignment: Alignment.centerLeft,
                      foregroundColor: context.theme.primaryColor,
                      iconColor: context.theme.primaryColor,
                      textStyle: context.textTheme.regular.copyWith(color: context.theme.primaryColor),
                    ),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (!isTradeDetails)
                      TextButton.icon(
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (_) => BlocProvider.value(
                                value: context.read<TradingCubit>(),
                                child: CancelOrderDialog(orderId: data.id!),
                              ),
                            ).then((value) {
                              if (value == true && context.mounted) {
                                Navigator.pop(context);
                              }
                            });
                          },
                          label: Text(
                            'revoke'.tr(),
                          ),
                          icon: Icon(LucideIcons.undo_2)),
                    TextButton.icon(
                      label: Text(
                        'quote'.tr(),
                      ),
                      icon: Icon(LucideIcons.chart_no_axes_combined),
                      onPressed: () {
                        Navigator.pop(context);
                        if (data.isCnFTrade) {
                          getIt<NavigatorService>().push(
                            AppRouter.routeFTradeAllInfo,
                            arguments: (
                              data.market != null ? CNFuturesMarketType.fromMarketCode(data.market!).idx : 0,
                              FTradeAllInfoTitlesType.quotation,
                              data.toFTradeListItemModel()
                            ),
                          );
                        } else {
                          getIt<NavigatorService>().push(
                            AppRouter.routeTradingCenter,
                            arguments: TradingArguments(
                              instrumentInfo: data.instrumentInfo,
                              selectedIndex: 1,
                              isIndexTrading: data.isIndex,
                            ),
                          );
                          context.read<TradingCubit>().setTradeType(TradeTabType.Quotes);
                        }
                      },
                    ),
                    TextButton.icon(
                      onPressed: () {
                        final securityType = SecurityType.fromCode(int.tryParse(data.securityType ?? '1') ?? 1);
                        return switch (securityType) {
                          SecurityType.stocks => {
                              Navigator.pop(context),
                              getIt<NavigatorService>().push(
                                AppRouter.routeTradingCenter,
                                arguments: TradingArguments(
                                  instrumentInfo: data.instrumentInfo,
                                  selectedIndex: 0,
                                  isIndexTrading: data.isIndex,
                                  contract: contract,
                                ),
                              )
                            },
                          SecurityType.indexx => {
                              Navigator.popUntil(context, (route) => route.isFirst),
                              context.read<MarketCubit>().updateMainHeaderTab(MarketSectionTab.stockIndex),
                              context.read<MainCubit>().selectedNavigationItem(BottomNavType.trade),
                              context.read<IndexTradeCubit>().updateSelectedIndex(
                                    context
                                        .read<IndexTradeCubit>()
                                        .state
                                        .indexes
                                        .indexWhere((element) => element.instrument == data.instrument),
                                    animate: true,
                                  )
                            },
                          SecurityType.cnFTrade => {
                              getIt<NavigatorService>().push(
                                AppRouter.routeFTradeAllInfo,
                                arguments: (
                                  data.market != null ? CNFuturesMarketType.fromMarketCode(data.market!).idx : 0,
                                  FTradeAllInfoTitlesType.trade,
                                  data.toFTradeListItemModel()
                                ),
                              ),
                            },
                          _ => debugPrint(''),
                        };
                      },
                      label: Text(
                        'trade2'.tr(),
                      ),
                      icon: Icon(LucideIcons.receipt_text),
                    ),
                    if (isTradeDetails && !isTrading)
                      TextButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          getIt<NavigatorService>().push(AppRouter.routeOrderDetail, arguments: {
                            'orderId': data.id,
                          });
                        },
                        label: Text(
                          'details'.tr(),
                        ),
                        icon: Icon(Icons.description_outlined),
                      ),
                  ],
                ),
              )
            ],
          ),
          SizedBox(height: 10.gh),
        ],
      ),
    );
  }

  String positionActionStr() {
    if (data.direction == 1 && data.tradeType == 1) {
      return "openLong".tr();
    }
    if (data.direction == 1 && data.tradeType == 2) {
      return "openShort".tr();
    }
    if (data.direction == 2 && data.tradeType == 1) {
      return "sellLong".tr();
    }
    if (data.direction == 2 && data.tradeType == 2) {
      return "sellShort".tr();
    }
    return "--";
  }

  Widget _buildKLineView() {
    return BlocSelector<TradingCubit, TradingState, (DataStatus, StockKlineResponse?, KlineOption?)>(
      selector: (state) => (state.klineDetailListStatus, state.klineDetailList, state.klineOption),
      builder: (context, state) {
        if (state.$1 == DataStatus.loading && state.$2?.data?.list == null) {
          return ShimmerWidget(height: 180.gh);
        }
        if (state.$1 == DataStatus.failed || state.$2?.data?.list == null) {
          return Center(child: Icon(Icons.error));
        }
        final isLine = state.$3?.type == "timeLine";
        final scaleX = switch (state.$3?.id) {
          "weekly-kline" => 0.5,
          "monthly-kline" => 0.5,
          "yearly-kline" => 1.0,
          "intraday" => 0.15,
          "5day" => 0.03,
          _ => 0.8,
        };

        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(8, 0, 8, 4),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(0, 10, 0, 0),
                  child: KChartWidget(
                    _processData(state.$2?.data?.list, isLine),
                    ChartStyle(),
                    ChartColors(
                      upColor: const Color(0xFFD2544F),
                      dnColor: const Color(0xFF5DAF78),
                      gridColor: Colors.transparent,
                      bgColor: context.theme.cardColor,
                      ma5Color: const Color(0xffE5B767),
                      ma10Color: const Color(0xff1FD1AC),
                      ma30Color: const Color(0xffB48CE3),
                      ma60Color: const Color(0xFFD5405D),
                    ),
                    getColorCallback: (value) => value.getValueColor(context),
                    mBaseHeight: 0.27.gsh,
                    isTrendLine: false,
                    scaleX: scaleX,
                    mainState: MainState.MA,
                    volHidden: true,
                    isTapShowInfoDialog: true,
                    showInfoDialog: false,
                    secondaryStateLi: {},
                    timeFormat: TimeFormat.YEAR_MONTH_DAY_WITH_HOUR,
                    verticalTextAlignment: VerticalTextAlignment.right,
                    isLine: isLine,
                    xFrontPadding: 0,
                    locale: context.locale.languageCode,
                    maDayList: const [5, 10, 20, 30, 60],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildFtradeKLineView() {
    return BlocSelector<FTradeKLineCubit, FTradeKLineState, (DataStatus, Map<String, FTradeKLineModel>)>(
      selector: (state) => (state.kLineDataStatus, state.kLineMap),
      builder: (context, newState) {
        FTradeInfoKLineModel? klineModelDetail = newState.$2['t_day']?.detail;
        List<FTradeKLineItem> klineModelList = newState.$2['t_day']?.list ?? [];

        final scaleX = 0.15;

        return Padding(
          padding: const EdgeInsets.fromLTRB(0, 10, 0, 0),
          child: KChartWidget(
            _processFTradeData(klineModelList),
            ChartStyle(),
            ChartColors(
              upColor: const Color(0xFFD2544F),
              dnColor: const Color(0xFF5DAF78),
              gridColor: Colors.transparent,
              bgColor: context.theme.cardColor,
              ma5Color: const Color(0xffE5B767),
              ma10Color: const Color(0xff1FD1AC),
              ma30Color: const Color(0xffB48CE3),
              ma60Color: const Color(0xFFD5405D),
            ),
            getColorCallback: (value) => value.getValueColor(context),
            mBaseHeight: 0.27.gsh,
            isTrendLine: false,
            scaleX: scaleX,
            mainState: MainState.MA,
            volHidden: false,
            isTapShowInfoDialog: true,
            secondaryStateLi: {},
            timeFormat: TimeFormat.YEAR_MONTH_DAY_WITH_HOUR,
            verticalTextAlignment: VerticalTextAlignment.right,
            isLine: true,
            xFrontPadding: 0,
            closePrice: klineModelDetail?.close,
            locale: context.locale.languageCode,
            maDayList: const [5, 10, 20, 30, 60],
          ),
        );
      },
    );
  }

  List<KLineEntity> _processData(List<KlineItem>? list, bool isLine) {
    if (list == null) return [];

    List<KLineEntity> klineEntities = [];
    double? previousPrice;

    for (var item in list) {
      final openPrice = previousPrice ?? (isLine ? item.price ?? 0 : item.open ?? 0);
      final klineEntity = KLineEntity.fromCustom(
        time: item.time != null ? item.time! * 1000 : 0,
        close: isLine ? item.price ?? 0 : item.close ?? 0,
        open: openPrice,
        high: isLine ? item.price ?? 0 : item.high ?? 0,
        low: isLine ? item.price ?? 0 : item.low ?? 0,
        vol: item.volume ?? 0,
        amount: item.price ?? 0,
      );

      klineEntities.add(klineEntity);
      previousPrice = item.price ?? 0;
    }

    DataUtil.calculate(klineEntities);
    return klineEntities;
  }

  List<KLineEntity> _processFTradeData(List<FTradeKLineItem>? list) {
    if (list == null || list.isEmpty) return [];

    final List<KLineEntity> klineEntities = [];
    double? previousPrice;
    final int offset = countryTimeOffsets['CN'] ?? 0;

    for (final item in list) {
      final open = previousPrice ?? item.price;

      final klineEntity = item.toKLineEntity(
        isTimeOrKline: true,
        openPrice: open,
        timeOffsetHours: offset,
      );

      klineEntities.add(klineEntity);
      previousPrice = item.price;
    }

    DataUtil.calculate(klineEntities);
    return klineEntities;
  }
}

class CancelOrderDialog extends StatelessWidget {
  const CancelOrderDialog({super.key, required this.orderId});

  final int orderId;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('cancelOrder'.tr()),
      content: Text('cancelOrderConfirmation'.tr()),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: Text('cancel'.tr()),
        ),
        TextButton(
          onPressed: () {
            context.read<TradingCubit>().cancelOrder(orderId);
            Navigator.pop(context, true);
          },
          child: Text(
            'confirm'.tr(),
            style: TextStyle(color: context.colorTheme.stockRed),
          ),
        ),
      ],
    );
  }
}
