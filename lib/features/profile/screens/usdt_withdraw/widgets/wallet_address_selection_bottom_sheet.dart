import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/profile/screens/usdt_withdraw/usdt_withdraw_cubit.dart';
import 'package:gp_stock_app/features/profile/screens/usdt_withdraw/usdt_withdraw_state.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';

import '../../../../../core/dependency_injection/injectable.dart';

class WalletAddressSelectionBottomSheet extends StatelessWidget {
  final Function(USDTWallet) onWalletSelected;

  const WalletAddressSelectionBottomSheet({
    super.key,
    required this.onWalletSelected,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UsdtWithdrawCubit, UsdtWithdrawState>(
      builder: (context, state) {
        final selectedNetwork = state.selectedChannel?.network ?? 1;
        final walletAddresses = state.usdtWalletAddresses;
        final selectedWallet = state.selectedWalletAddress;
        final filteredWallets = walletAddresses.where((wallet) => wallet.network == selectedNetwork).toList();
        return Container(
          height: MediaQuery.of(context).size.height * 0.6,
          decoration: BoxDecoration(
            color: context.theme.cardColor,
          ),
          child: Column(
            children: [
              Container(
                height: 46.gw,
                padding: EdgeInsets.symmetric(horizontal: 16.gw),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: Icon(
                        Icons.arrow_back_ios,
                        color: context.colorTheme.textTitle,
                        size: 18.gsp,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'selectAddress'.tr(),
                        textAlign: TextAlign.center,
                        style: context.textTheme.regular.copyWith(
                          fontSize: 14.gsp,
                          fontWeight: FontWeight.w400,
                          color: context.colorTheme.textTitle,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () async {
                        await getIt<NavigatorService>().push(AppRouter.routeUSDTWalletManage).then((result) {
                          if (context.mounted) {
                            context.read<UsdtWithdrawCubit>().fetchUsdtWalletAddressList();
                          }
                        });
                      },
                      child: Text(
                        'manage'.tr(),
                        style: context.textTheme.regular.copyWith(
                          fontSize: 14.gsp,
                          fontWeight: FontWeight.w500,
                          color: context.theme.primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Wallet list
              Expanded(
                child: filteredWallets.isEmpty
                    ? _buildEmptyState(context)
                    : ListView.builder(
                        padding:
                            EdgeInsets.fromLTRB(14.gw, 0, 14.gw, max(MediaQuery.of(context).padding.bottom, 10.gw)),
                        itemCount: filteredWallets.length,
                        itemBuilder: (context, index) {
                          final wallet = filteredWallets[index];
                          final isSelected = selectedWallet?.id == wallet.id;
                          return _buildWalletItem(context, wallet, isSelected);
                        },
                      ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 空状态图标
          Image.asset(
            'assets/images/icon_usdt_wallet_empty.png',
            width: 120.gw,
            height: 120.gw,
          ),
          SizedBox(height: 10.gw),
          // 提示文字
          Text(
            'noUsdtAddressAdded'.tr(), // 未添加USDT地址
            style: context.textTheme.regular.copyWith(
              color: context.theme.hintColor,
            ),
          ),
          SizedBox(height: 100.gw),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 15.gw),
            child: CommonButton(
              onPressed: () async {
                getIt<NavigatorService>().push(AppRouter.routeUSDTWalletEdit).then((result) {
                  if (context.mounted) {
                    context.read<UsdtWithdrawCubit>().fetchUsdtWalletAddressList();
                  }
                });
              },
              title: 'addAddress'.tr(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWalletItem(BuildContext context, USDTWallet wallet, bool isSelected) {
    return GestureDetector(
      onTap: () {
        onWalletSelected(wallet);
        Navigator.pop(context);
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 12.gh),
        padding: EdgeInsets.all(14.gw),
        decoration: BoxDecoration(
          color: context.theme.inputDecorationTheme.fillColor,
          borderRadius: BorderRadius.circular(8.gr),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _getNetworkName(wallet.network),
                  style: context.textTheme.title.w600,
                ),
                Container(
                  width: 18.gw,
                  height: 18.gh,
                  decoration: BoxDecoration(
                    color: isSelected ? context.theme.primaryColor : Colors.transparent,
                    border: Border.all(
                      color: context.theme.primaryColor,
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(9.gr),
                  ),
                  child: isSelected
                      ? Icon(
                          Icons.check,
                          size: 12.gsp,
                          color: Colors.white,
                        )
                      : null,
                ),
              ],
            ),
            SizedBox(height: 12.gh),
            Text(
              wallet.walletAddress,
              style: context.textTheme.regular.copyWith(
                fontSize: 12.gsp,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getNetworkName(int network) {
    switch (network) {
      case 1:
        return 'TRC20';
      case 2:
        return 'ERC20';
      case 3:
        return 'BEP20';
      default:
        return 'Unknown';
    }
  }
}
