import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';

import 'usdt_wallet_manage_cubit.dart';
import 'usdt_wallet_manage_state.dart';
import 'package:gp_stock_app/shared/widgets/alert_dilaog/common_dialog.dart';

class UsdtWalletManagePage extends StatelessWidget {
  const UsdtWalletManagePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => UsdtWalletManageCubit(),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<UsdtWalletManageCubit>(context);
    final safeBottomInset = MediaQuery.of(context).padding.bottom;
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'usdtAddressManagement'.tr(), // USDT地址管理
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 主要内容区域
            Expanded(
              child: BlocBuilder<UsdtWalletManageCubit, UsdtWalletManageState>(
                builder: (context, state) {
                  if (state.isLoading) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  if (state.error != null) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 48.gw,
                            color: context.colorTheme.stockRed,
                          ),
                          SizedBox(height: 16.gw),
                          Text(
                            'loadFailed'.tr(), // 加载失败
                            style: context.textTheme.primary.fs16.w500,
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 8.gw),
                          Text(
                            state.error!,
                            style: context.textTheme.regular,
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 16.gw),
                          ElevatedButton(
                            onPressed: () => cubit.fetchWalletList(),
                            child: Text('retry'.tr()), // 重试
                          ),
                        ],
                      ),
                    );
                  }

                  if (state.walletList.isEmpty) {
                    return _buildEmptyState(context);
                  }

                  return _buildWalletList(context, state, cubit);
                },
              ),
            ),
          ],
        ),
      ),

      // 底部添加地址按钮
      bottomNavigationBar: Container(
        padding: EdgeInsets.only(
          left: 16.gw,
          right: 16.gw,
          bottom: 25.gw,
          top: 16.gw,
        ),
        child: ElevatedButton(
          onPressed: () async {
            await getIt<NavigatorService>().push(AppRouter.routeUSDTWalletEdit);
            cubit.fetchWalletList();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: context.colorTheme.textHighlight,
            foregroundColor: context.colorTheme.textSecondary,
            minimumSize: Size(double.infinity, 48.gw),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.gw),
            ),
          ),
          child: Text(
            'addAddress'.tr(), // 添加地址
            style: context.textTheme.primary.fs16.w600.copyWith(
              color: context.colorTheme.textSecondary,
            ),
          ),
        ),
      ),
    );
  }

  /// 空状态界面
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 空状态图标
          Image.asset(
            'assets/images/icon_usdt_wallet_empty.png',
            width: 200.gw,
            height: 200.gw,
          ),

          SizedBox(height: 13.gw),
          // 提示文字
          Text(
            'noUsdtAddressAdded'.tr(), // 未添加USDT地址
            style: context.textTheme.regular.copyWith(
              color: context.theme.hintColor,
            ),
          ),
        ],
      ),
    );
  }

  /// 钱包列表界面
  Widget _buildWalletList(
    BuildContext context,
    UsdtWalletManageState state,
    UsdtWalletManageCubit cubit,
  ) {
    return ListView.separated(
      padding: EdgeInsets.all(16.gw),
      itemCount: state.walletList.length,
      itemBuilder: (context, index) {
        final wallet = state.walletList[index];
        return _buildWalletCell(context, wallet, cubit);
      },
      separatorBuilder: (_, __) {
        return SizedBox(height: 12.gw);
      },
    );
  }

  /// 单个钱包项
  Widget _buildWalletCell(
    BuildContext context,
    USDTWallet wallet,
    UsdtWalletManageCubit cubit,
  ) {
    final isDefault = wallet.isWithdrawDefault == 1;

    return GestureDetector(
      onTap: () async {
        await getIt<NavigatorService>().push(AppRouter.routeUSDTWalletEdit, arguments: wallet);
        cubit.fetchWalletList();
      },
      child: ShadowBox(
        padding: EdgeInsets.symmetric(horizontal: 14.gw),
        borderRadius: BorderRadius.circular(8.gr),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 7.gw),
            // 顶部行：网络类型和删除按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 网络类型标签
                Text(
                  cubit.getNetworkDisplayName(wallet.network),
                  style: context.textTheme.primary.w500.copyWith(
                    color: context.colorTheme.tabActive,
                  ),
                ),

                // 删除按钮
                GestureDetector(
                  onTap: () => _showDeleteConfirmDialog(context, wallet, cubit),
                  child: Container(
                    padding: EdgeInsets.only(left: 10.gw),
                    child: IconHelper.loadAsset(
                      "assets/svg/icon_wallet_delete.svg",
                      width: 24.gw,
                      height: 24.gw,
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: 12.gw),

            // 钱包地址
            Text(
              wallet.walletAddress,
              style: context.textTheme.regular.fs12,
            ),

            SizedBox(height: 16.gw),

            Divider(height: 1, color: context.theme.dividerColor),
            // 底部行：设置默认和默认状态
            SizedBox(
              height: 44.gw,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 设置默认按钮
                  Text(
                    'setAsDefault'.tr(), // 设为默认
                    style: context.textTheme.primary.w500,
                  ),

                  // 默认状态指示器
                  GestureDetector(
                    onTap: isDefault ? null : () => cubit.setDefaultWallet(wallet.id),
                    child: Container(
                      width: 40.gw,
                      height: 40.gw,
                      alignment: Alignment.centerRight,
                      child: isDefault
                          ? IconHelper.loadAsset("assets/svg/icon_checkmark_circle_selected.svg",
                              width: 18.gw, height: 18.gw, color: context.theme.primaryColor)
                          : IconHelper.loadAsset("assets/svg/icon_checkmark_circle_unselected.svg",
                              width: 18.gw, height: 18.gw),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmDialog(
    BuildContext context,
    USDTWallet wallet,
    UsdtWalletManageCubit cubit,
  ) {
    CommonDialog(
      context,
      title: 'confirmDelete'.tr(),
      // 确认删除
      content: 'confirmDeleteUsdtAddress'.tr(),
      // 确定要删除这个USDT地址吗？
      sureBtnTitle: 'delete'.tr(),
      // 删除
      sureBtnTitleStyle: context.textTheme.primary.copyWith(
        color: context.colorTheme.stockRed,
      ),
      complete: () => cubit.deleteWallet(wallet.id),
    ).show();
  }
}
