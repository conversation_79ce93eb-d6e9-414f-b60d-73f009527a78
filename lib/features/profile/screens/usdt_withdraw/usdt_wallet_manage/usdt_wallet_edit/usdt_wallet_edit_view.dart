import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/switch/common_switch.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';

import 'usdt_wallet_edit_cubit.dart';
import 'usdt_wallet_edit_state.dart';

class UsdtWalletEditPage extends StatefulWidget {
  final USDTWallet? wallet; // 如果为null则为新增，否则为编辑

  const UsdtWalletEditPage({
    super.key,
    this.wallet,
  });

  @override
  State<UsdtWalletEditPage> createState() => _UsdtWalletEditPageState();
}

class _UsdtWalletEditPageState extends State<UsdtWalletEditPage> {
  final TextEditingController _addressController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isFormValid = false;

  @override
  void initState() {
    super.initState();
    // 如果是编辑模式，预填充数据
    if (widget.wallet != null) {
      _addressController.text = widget.wallet!.walletAddress;
    }
    _validateForm();
  }

  @override
  void dispose() {
    _addressController.dispose();
    super.dispose();
  }

  void _validateForm() {
    setState(() {
      _isFormValid = _addressController.text.isNotEmpty;
    });
  }

  @override
  Widget build(BuildContext context) {
    final safeBottomInset = MediaQuery.of(context).padding.bottom;
    return BlocProvider(
      create: (context) => UsdtWalletEditCubit(wallet: widget.wallet),
      child: BlocBuilder<UsdtWalletEditCubit, UsdtWalletEditState>(
        builder: (context, state) {
          return Scaffold(
            appBar: AppBar(
              title: Text(
                widget.wallet == null
                    ? 'addUsdtAddress'.tr() // 添加USDT地址
                    : 'editUsdtAddress'.tr(), // 编辑USDT地址
              ),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back_ios),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
            body: Padding(
              padding: EdgeInsets.fromLTRB(14.gw, 10.gw, 14.gw, 25.gw),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    ShadowBox(
                      borderRadius: BorderRadius.circular(8.gr),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // 提现地址输入区域
                          _buildAddressInputSection(context, state),

                          SizedBox(height: 12.gw),

                          // 充值网络选择区域
                          _buildNetworkSelectionSection(context, state),
                        ],
                      ),
                    ),

                    SizedBox(height: 12.gw),

                    // 设为默认地址选项
                    _buildDefaultAddressSection(context, state),

                    const Spacer(),
                    // 提交按钮
                    CommonButton(
                      onPressed: _isFormValid && !state.isAdding && !state.isEditing
                          ? () => _handleSubmit(context, state)
                          : null,
                      title: state.isAdding || state.isEditing
                          ? 'loading'.tr()
                          : (widget.wallet == null
                              ? 'addAddress'.tr() // 添加地址
                              : 'editAddress'.tr()),
                      // 编辑地址
                      color: context.colorTheme.textHighlight,
                      radius: 8.gw,
                      textColor: context.colorTheme.textSecondary,
                      fontSize: 16.gw,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// 构建地址输入区域
  Widget _buildAddressInputSection(BuildContext context, UsdtWalletEditState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'withdrawalAddress'.tr(), // 提现地址
          style: context.textTheme.regular.w600,
        ),
        SizedBox(height: 8.gw),
        TextFieldWidget(
          controller: _addressController,
          contentPadding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 10.gw),
          borderType: TextFieldBorderType.underline,
          fillColor: Colors.transparent,
          hintText: 'pleaseEnterWithdrawalAddress'.tr(),
          // 请输入提现地址
          onChanged: (value) {
            context.read<UsdtWalletEditCubit>().updateWalletAddress(value ?? '');
            _validateForm();
          },
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CommonButton(
                title: 'paste'.tr(),
                // 粘贴
                width: 48.gw,
                height: 28.gw,
                fontSize: 14.gsp,
                radius: 6.gr,
                onPressed: () => _pasteAddress(context),
              ),
            ],
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'pleaseEnterWalletAddress'.tr(); // 请输入钱包地址
            }
            return null;
          },
        ),
      ],
    );
  }

  /// 构建网络选择区域
  Widget _buildNetworkSelectionSection(BuildContext context, UsdtWalletEditState state) {
    final cubit = context.read<UsdtWalletEditCubit>();
    final networkOptions = cubit.getNetworkOptions();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'rechargeNetwork'.tr(), // 充值网络
          style: context.textTheme.regular.w600,
        ),
        SizedBox(height: 8.gw),
        CommonDropdown<Map<String, dynamic>>(
          hintText: 'pleaseSelectRechargeNetwork'.tr(),
          // 请选择充值网络
          dropDownValue: networkOptions
              .map((network) => DropDownValue(
                    id: network['id'].toString(),
                    value: network['name'],
                  ))
              .toList(),
          selectedItem: networkOptions
              .where((network) => network['id'] == state.selectedNetwork)
              .map((network) => DropDownValue(
                    id: network['id'].toString(),
                    value: network['name'],
                  ))
              .firstOrNull,
          onChanged: (value) {
            if (value != null) {
              final networkId = int.tryParse(value.id ?? '');
              if (networkId != null) {
                cubit.updateSelectedNetwork(networkId);
              }
            }
          },
          showSearchBox: false,
          itemBuilder: (context, item, isDisabled, isSelected) {
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 16.gw),
              child: Text(
                item.value ?? '',
                style: context.textTheme.primary.fs16,
              ),
            );
          },
        ),
      ],
    );
  }

  /// 构建设为默认地址选项
  Widget _buildDefaultAddressSection(BuildContext context, UsdtWalletEditState state) {
    return ShadowBox(
      padding: EdgeInsets.symmetric(horizontal: 15.gw),
      height: 53.gw,
      borderRadius: BorderRadius.circular(8.gr),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'setAsDefaultUsdtAddress'.tr(), // 设为默认USDT地址
            style: context.textTheme.title.w600,
          ),
          CommonSwitch(
            value: state.isDefaultAddress,
            onChanged: (value) {
              context.read<UsdtWalletEditCubit>().updateIsDefaultAddress(value);
            },
          ),
        ],
      ),
    );
  }

  /// 处理提交操作
  void _handleSubmit(BuildContext context, UsdtWalletEditState state) {
    if (_formKey.currentState!.validate()) {
      final cubit = context.read<UsdtWalletEditCubit>();
      if (widget.wallet == null) {
        // 新增模式
        cubit.addWalletAddress().then((_) {
          if (mounted) {
            Navigator.of(context).pop(true); // 返回true表示操作成功
          }
        });
      } else {
        // 编辑模式
        cubit.editWalletAddress().then((_) {
          if (mounted) {
            Navigator.of(context).pop(true); // 返回true表示操作成功
          }
        });
      }
    }
  }

  /// 粘贴地址
  void _pasteAddress(BuildContext context) async {
    try {
      final data = await Clipboard.getData(Clipboard.kTextPlain);
      if (data?.text != null && data!.text!.isNotEmpty) {
        _addressController.text = data.text!;
        if (context.mounted) {
          context.read<UsdtWalletEditCubit>().updateWalletAddress(data.text!);
        }
        _validateForm();
      }
    } catch (e) {
      // 粘贴失败，忽略错误
    }
  }
}
