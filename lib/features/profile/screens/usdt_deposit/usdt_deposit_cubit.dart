import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/deposit.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

import 'usdt_deposit_state.dart';

class UsdtDepositCubit extends Cubit<UsdtDepositState> {
  UsdtDepositCubit() : super(const UsdtDepositState()) {
    fetchUsdtList();
  }

  Future<void> fetchUsdtList() async {
    emit(state.copyWith(isLoadingChannels: true));
    try {
      final result = await DepositApi.fetchUsdtList();
      emit(state.copyWith(
        usdtList: result,
        selectedChannel: result.isNotEmpty ? result.first : null,
      ));
    } finally {
      emit(state.copyWith(isLoadingChannels: false));
    }
  }

  void selectChannel(int channelId) {
    final selectedChannel = state.usdtList
        .where(
          (channel) => channel.id == channelId,
        )
        .firstOrNull;
    if (selectedChannel != null) {
      emit(state.copyWith(selectedChannel: selectedChannel));
    }
  }

  void updateAmount(String amount) {
    emit(state.copyWith(selectedAmount: amount));
  }

  void updateAmountValidation(bool isValid) {
    emit(state.copyWith(isAmountValid: isValid));
  }

  void clearForm() {
    emit(state.copyWith(selectedChannel: null, selectedAmount: null));
  }

  /// 提交USDT充值申请
  Future<void> submitDeposit(String amount) async {
    if (state.selectedChannel == null) {
      GPEasyLoading.showToast('deposit_select_channel'.tr());
      return;
    }

    try {
      // 开始提交
      emit(state.copyWith(isSubmitting: true));

      // 调用API
      final res = await DepositApi.applyUsdtDepost(
        id: state.selectedChannel!.id,
        amount: double.parse(amount),
      );

      if (res != null) {
        // 提交成功
        GPEasyLoading.showToast('apply_deposit_success'.tr());

        getIt<NavigatorService>().pushReplace(AppRouter.routeUSDTDepositOrder, arguments: res);
        
        
        // 清空表单
        clearForm();
        _amountController?.clear();
      }
    } catch (e) {
      // 处理异常
      GPEasyLoading.showToast('apply_deposit_failed'.tr());
    } finally {
      // 结束提交状态
      emit(state.copyWith(isSubmitting: false));
    }
  }

  // 临时存储TextEditingController的引用，用于清空输入
  TextEditingController? _amountController;

  void setAmountController(TextEditingController controller) {
    _amountController = controller;
  }
}
