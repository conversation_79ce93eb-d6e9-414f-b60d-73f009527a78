import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/clipboardTool.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/string_util.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:qr_flutter/qr_flutter.dart';

import 'usdt_deposit_in_process_cubit.dart';
import 'usdt_deposit_in_process_state.dart';

class UsdtDepositInProcessPage extends StatelessWidget {
  const UsdtDepositInProcessPage({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<UsdtDepositInProcessCubit>();
    return BlocBuilder<UsdtDepositInProcessCubit, UsdtDepositInProcessState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: context.theme.scaffoldBackgroundColor,
          appBar: AppBar(
            backgroundColor: context.theme.cardColor,
            surfaceTintColor: Colors.transparent,
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: context.colorTheme.textPrimary),
              onPressed: () => Navigator.of(context).pop(),
            ),
            title: Text(
              'usdt_recharge'.tr(), // USDT充值
              style: context.textTheme.title.copyWith(
                color: context.colorTheme.textPrimary,
              ),
            ),
            centerTitle: true,
          ),
          body: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 14.gw),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 订单详情卡片
                  ShadowBox(
                    borderRadius: BorderRadius.circular(8.gr),
                    margin: EdgeInsets.only(top: 12.gw),
                    padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 12.gw),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      spacing: 0,
                      children: [
                        // 订单号
                        _buildOrderInfoRow(context, 'order_number'.tr(), state.order.orderNo, true),
                        // 充值金额
                        _buildOrderInfoRow(
                            context, 'recharge_amount'.tr(), "¥${state.order.orderAmount.formattedMoney}", true),
                        // 充币数量
                        _buildOrderInfoRow(context, 'deposit_quantity'.tr(), state.order.usdtAmount.toString(), true),
                        // 汇率
                        _buildOrderInfoRow(context, 'exchange_rate'.tr(), '1:${state.order.exchangeRate}', false),
                        // 充币地址
                        _buildOrderInfoRow(context, 'deposit_address'.tr(), state.order.rechargeAddress, true),
                        // 充值网络
                        _buildOrderInfoRow(context, 'recharge_network'.tr(), state.order.networkName, true),

                        27.verticalSpace,
                        Text(
                          'copy_and_verify_notice'.tr(), // 请务必完整复制并仔细核对,以免造成损失
                          style: context.textTheme.regular.fs12.copyWith(
                            color: context.theme.hintColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                  14.verticalSpace,
                  Text(
                    'deposit_qr_code'.tr(), // 充币二维码
                    style: context.textTheme.title.fs12,
                  ),

                  // 二维码区域
                  Container(
                    width: double.infinity,
                    alignment: Alignment.center,
                    child: Container(
                      width: 148.gw,
                      height: 148.gw,
                      padding: EdgeInsets.all(5.gw),
                      decoration: BoxDecoration(
                          color: context.theme.cardColor,
                          borderRadius: BorderRadius.circular(11.gr),
                          boxShadow: [
                            BoxShadow(
                              color: context.theme.primaryColor.withValues(alpha: 0.08),
                              blurRadius: 8.gw,
                              offset: Offset(0, 4.gw),
                            )
                          ]),
                      child: Center(
                        child: QrImageView(
                          data: state.order.rechargeAddress,
                          version: QrVersions.auto,
                          size: 146.gw,
                          // padding: EdgeInsets.zero,
                        ),
                      ),
                    ),
                  ),
                  34.verticalSpace,
                  CommonButton(
                    onPressed: () => cubit.contactCustomerService(context),
                    title: 'recharge_completed_contact_customer_service'.tr(), // 已完成充值,联系客服确认订单
                    enable: !state.isSubmitting,
                    showLoading: state.isSubmitting,
                  ),
                  16.verticalSpace,
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildOrderInfoRow(BuildContext context, String label, String value, bool showCopyIcon) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10.gw),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: context.textTheme.title,
          ),
          SizedBox(width: 15.gw),
          Expanded(
            child: GestureDetector(
              onTap: showCopyIcon
                  ? () {
                      ClipboardTool.setData(value);
                    }
                  : null,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text(
                      value,
                      style: context.textTheme.primary.w800.ffAkz,
                      textAlign: TextAlign.end,
                    ),
                  ),
                  if (showCopyIcon) ...[
                    4.horizontalSpace,
                    Padding(
                      padding: EdgeInsets.only(top: 2.gw),
                      child: IconHelper.loadAsset(
                        "assets/svg/icon_order_copy.svg",
                        width: 16.gw,
                        height: 16.gw,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
