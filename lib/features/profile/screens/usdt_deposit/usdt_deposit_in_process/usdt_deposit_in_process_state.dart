import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/deposit/usdt_channel.dart';

class UsdtDepositInProcessState extends Equatable {
  final UsdtRechargeOrder order;
  final bool isSubmitting;

  const UsdtDepositInProcessState({
    required this.order,
    this.isSubmitting = false,
  });

  UsdtDepositInProcessState copyWith({
    UsdtRechargeOrder? order,
    bool? isSubmitting,
  }) {
    return UsdtDepositInProcessState(
      order: order ?? this.order,
      isSubmitting: isSubmitting ?? this.isSubmitting,
    );
  }

  @override
  List<Object?> get props => [
        order,
        isSubmitting,
      ];
}
