part of 'market_cubit.dart';

class MarketState extends Equatable {
  const MarketState({
    this.depthQuoteFetchStatus = DataStatus.idle,
    this.depthQuoteResponse,
    this.plateFetchStatusA = DataStatus.idle,
    this.plateResponseA,
    this.plateFetchStatusB = DataStatus.idle,
    this.plateResponseB,
    this.stockFetchStatus = DataStatus.idle,
    this.stockResponse,
    this.componentStockResponse,
    this.klineFetchStatus = DataStatus.idle,
    this.coreKlineList,
    this.miniKlineList,
    this.distFetchStatus = DataStatus.idle,
    this.distResponse,
    this.tableData,
    this.tableFetchStatus = DataStatus.idle,
    this.selectedTableTab = 0, // 0: Hot List, 1: Gainers, 2: Losers
    this.isPaginating = false,
    this.selectedPeriod = "realtime",
    this.selectedTodaysTab = TodaysTab.aShares,
    this.selectedMarketTableType = MarketType.she<PERSON>hen,
    this.sortByPriceAsc,
    this.sortByChangeAsc,
    this.plateInfoFetchStatus = DataStatus.idle,
    this.plateInfoResponse,
    this.sortByPricePlateInfoAsc,
    this.sortByChangePlateInfoAsc,
    this.sortByChangePlateListAsc,
    this.pageNumber = 1,
    this.selectedMarketSectionTab = MarketSectionTab.stock,
    this.paginationStatus = DataStatus.idle,
    this.hasReachedMax = false,
    this.currentPageSize = 20,
    this.sortType = 0,
    this.order = 'DESC',
  });

  final DataStatus depthQuoteFetchStatus;
  final DepthQuoteModel? depthQuoteResponse;
  final DataStatus plateFetchStatusA;
  final PlateResponse? plateResponseA;
  final DataStatus plateFetchStatusB;
  final PlateResponse? plateResponseB;
  final DataStatus stockFetchStatus;
  final ComponentStockResponse? componentStockResponse;
  final StockResponse? stockResponse;
  final DataStatus klineFetchStatus;
  final List<StockKlineData>? coreKlineList;
  final List<StockKlineData>? miniKlineList;
  final DataStatus distFetchStatus;
  final DistResponse? distResponse;
  final StockTableResponse? tableData;
  final DataStatus tableFetchStatus;
  final int selectedTableTab;
  final bool isPaginating;
  final String selectedPeriod;
  final TodaysTab selectedTodaysTab;
  final MarketType? selectedMarketTableType;
  final MarketSectionTab selectedMarketSectionTab;
  final SortType? sortByPriceAsc;
  final SortType? sortByChangeAsc;
  final SortType? sortByPricePlateInfoAsc;
  final SortType? sortByChangePlateInfoAsc;
  final SortType? sortByChangePlateListAsc;
  final DataStatus plateInfoFetchStatus;
  final PlateInfoResponse? plateInfoResponse;
  final int pageNumber;
  final DataStatus paginationStatus;
  final bool hasReachedMax;
  final int currentPageSize;
  final int sortType;
  final String order;
  @override
  List<Object?> get props => [
        depthQuoteFetchStatus,
        depthQuoteResponse,
        plateFetchStatusA,
        plateResponseA,
        plateFetchStatusB,
        plateResponseB,
        stockFetchStatus,
        stockResponse,
        klineFetchStatus,
        componentStockResponse,
        coreKlineList,
        miniKlineList,
        distFetchStatus,
        distResponse,
        tableData,
        tableFetchStatus,
        selectedTableTab,
        selectedPeriod,
        isPaginating,
        selectedTodaysTab,
        selectedMarketTableType,
        selectedMarketSectionTab,
        sortByPriceAsc,
        sortByChangeAsc,
        sortByPricePlateInfoAsc,
        sortByChangePlateInfoAsc,
        sortByChangePlateListAsc,
        plateInfoFetchStatus,
        plateInfoResponse,
        pageNumber,
        paginationStatus,
        hasReachedMax,
        currentPageSize,
        sortType,
        order,
      ];

  MarketState copyWith({
    DataStatus? depthQuoteFetchStatus,
    DepthQuoteModel? depthQuoteResponse,
    DataStatus? plateFetchStatusA,
    PlateResponse? plateResponseA,
    DataStatus? plateFetchStatusB,
    PlateResponse? plateResponseB,
    DataStatus? stockFetchStatus,
    StockResponse? stockResponse,
    ComponentStockResponse? componentStockResponse,
    DataStatus? klineFetchStatus,
    List<StockKlineData>? coreKlineList,
    List<StockKlineData>? miniKlineList,
    DataStatus? distFetchStatus,
    DistResponse? distResponse,
    StockTableResponse? tableData,
    DataStatus? tableFetchStatus,
    int? selectedTableTab,
    bool? isPaginating,
    String? selectedPeriod,
    TodaysTab? selectedTodaysTab,
    MarketType? selectedMarketTableType,
    MarketSectionTab? selectedMarketSectionTab,
    SortType? sortByPriceAsc,
    SortType? sortByChangeAsc,
    SortType? sortByPricePlateInfoAsc,
    SortType? sortByChangePlateInfoAsc,
    SortType? sortByChangePlateListAsc,
    bool resetSortByChange = false,
    bool resetSortByPrice = false,
    bool resetSortByPricePlateInfo = false,
    bool resetSortByChangePlateInfo = false,
    DataStatus? plateInfoFetchStatus,
    PlateInfoResponse? plateInfoResponse,
    bool resetSelectedMarketTableType = false,
    bool resetSortByChangePlateList = false,
    int? pageNumber,
    DataStatus? paginationStatus,
    bool? hasReachedMax,
    int? currentPageSize,
    int? sortType,
    String? order,
  }) {
    return MarketState(
      depthQuoteFetchStatus: depthQuoteFetchStatus ?? this.depthQuoteFetchStatus,
      depthQuoteResponse: depthQuoteResponse ?? this.depthQuoteResponse,
      plateFetchStatusA: plateFetchStatusA ?? this.plateFetchStatusA,
      plateResponseA: plateResponseA ?? this.plateResponseA,
      plateFetchStatusB: plateFetchStatusB ?? this.plateFetchStatusB,
      plateResponseB: plateResponseB ?? this.plateResponseB,
      stockFetchStatus: stockFetchStatus ?? this.stockFetchStatus,
      stockResponse: stockResponse ?? this.stockResponse,
      klineFetchStatus: klineFetchStatus ?? this.klineFetchStatus,
      coreKlineList: coreKlineList ?? this.coreKlineList,
      miniKlineList: miniKlineList ?? this.miniKlineList,
      distFetchStatus: distFetchStatus ?? this.distFetchStatus,
      distResponse: distResponse ?? this.distResponse,
      componentStockResponse: componentStockResponse ?? this.componentStockResponse,
      tableData: tableData ?? this.tableData,
      tableFetchStatus: tableFetchStatus ?? this.tableFetchStatus,
      selectedTableTab: selectedTableTab ?? this.selectedTableTab,
      isPaginating: isPaginating ?? this.isPaginating,
      selectedPeriod: selectedPeriod ?? this.selectedPeriod,
      selectedTodaysTab: selectedTodaysTab ?? this.selectedTodaysTab,
      selectedMarketTableType:
          resetSelectedMarketTableType ? null : selectedMarketTableType ?? this.selectedMarketTableType,
      sortByChangeAsc: resetSortByChange ? null : sortByChangeAsc ?? this.sortByChangeAsc,
      sortByPriceAsc: resetSortByPrice ? null : sortByPriceAsc ?? this.sortByPriceAsc,
      sortByPricePlateInfoAsc:
          resetSortByPricePlateInfo ? null : sortByPricePlateInfoAsc ?? this.sortByPricePlateInfoAsc,
      sortByChangePlateInfoAsc:
          resetSortByChangePlateInfo ? null : sortByChangePlateInfoAsc ?? this.sortByChangePlateInfoAsc,
      sortByChangePlateListAsc:
          resetSortByChangePlateList ? null : sortByChangePlateListAsc ?? this.sortByChangePlateListAsc,
      selectedMarketSectionTab: selectedMarketSectionTab ?? this.selectedMarketSectionTab,
      plateInfoFetchStatus: plateInfoFetchStatus ?? this.plateInfoFetchStatus,
      plateInfoResponse: plateInfoResponse ?? this.plateInfoResponse,
      pageNumber: pageNumber ?? this.pageNumber,
      paginationStatus: paginationStatus ?? this.paginationStatus,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPageSize: currentPageSize ?? this.currentPageSize,
      sortType: sortType ?? this.sortType,
      order: order ?? this.order,
    );
  }
}
