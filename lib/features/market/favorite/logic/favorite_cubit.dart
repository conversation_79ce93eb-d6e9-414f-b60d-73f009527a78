import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/watchlist.dart';
import 'package:gp_stock_app/features/market/watch_list/logic/watch_list_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import 'favorite_state.dart';

class FavoriteCubit extends Cubit<FavoriteState> {
  final String symbol;
  final String market;
  final String securityType;

  FavoriteCubit({
    required this.symbol,
    required this.market,
    required this.securityType,
    required bool initialAdded,
  }) : super(FavoriteState(added: initialAdded));

  /// Update the favorite status based on the search response data
  void updateFromSearchData(bool isWatchlist) {
    if (state.added != isWatchlist) {
      emit(state.copyWith(added: isWatchlist));
    }
  }

  /// 添加到收藏
  /// Add to favorites
  Future<void> addToFavourite() async {
    // 先更新本地状态，提供良好的用户体验
    // Update local state first for good UX
    emit(state.copyWith(
      added: true,
      addToFavoriteStatus: DataStatus.loading,
    ));

    try {
      final flag = await WatchlistApi.addToWatchList(
        symbol: symbol,
        market: market,
        putSort: 0,
        securityType: securityType,
        sort: 0,
      );

      if (flag) {
        emit(state.copyWith(
          addToFavoriteStatus: DataStatus.success,
        ));
        // 刷新全局自选股列表
        // Refresh global watchlist
        getIt<WatchListCubit>().getWatchList();
      } else {
        // API失败，回滚本地状态
        // API failed, rollback local state
        emit(state.copyWith(
          added: false,
          addToFavoriteStatus: DataStatus.failed,
          error: 'Failed to add to favorites',
        ));
      }
    } catch (e) {
      // 异常情况，回滚本地状态
      // Exception, rollback local state
      emit(state.copyWith(
        added: false,
        addToFavoriteStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  /// 从收藏移除
  /// Remove from favorites
  Future<void> removeFromFavourite() async {
    // 先更新本地状态，提供良好的用户体验
    // Update local state first for good UX
    emit(state.copyWith(
      added: false,
      removeFromFavoriteStatus: DataStatus.loading,
    ));

    try {
      final flag = await WatchlistApi.removeFromWatchListBySymbol(
        symbol: symbol,
        securityType: securityType,
        market: market,
      );

      if (flag) {
        emit(state.copyWith(
          removeFromFavoriteStatus: DataStatus.success,
        ));
        // 刷新全局自选股列表
        // Refresh global watchlist
        getIt<WatchListCubit>().getWatchList();
      } else {
        // API失败，回滚本地状态
        // API failed, rollback local state
        emit(state.copyWith(
          added: true,
          removeFromFavoriteStatus: DataStatus.failed,
          error: 'Failed to remove from favorites',
        ));
      }
    } catch (e) {
      // 异常情况，回滚本地状态
      // Exception, rollback local state
      emit(state.copyWith(
        added: true,
        removeFromFavoriteStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  /// 切换收藏状态
  /// Toggle favorite status
  Future<void> toggleFavorite() async {
    if (state.added) {
      await removeFromFavourite();
    } else {
      await addToFavourite();
    }
  }
}
