import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/f_trade_list_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/logic/f_trade_list_cubit.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/features/market/index_trade_screen.dart';
import 'package:gp_stock_app/features/market/stock_screen.dart';
import 'package:gp_stock_app/features/market/watch_list/screens/watch_list_screen.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/constants/enums/trading_mode.dart';
import 'package:gp_stock_app/shared/logic/sys_settings/sys_settings_cubit.dart';
import 'package:gp_stock_app/shared/widgets/tab/common_tab_bar.dart';

import '../../shared/mixin/animation.dart';
import 'logic/market/market_cubit.dart';

class MarketSectionScreen extends StatefulWidget {
  final bool showBackButton;
  final ScrollController? scrollController;
  const MarketSectionScreen({
    super.key,
    this.showBackButton = false,
    this.scrollController,
  });

  @override
  State<MarketSectionScreen> createState() => _MarketSectionScreenState();
}

class _MarketSectionScreenState extends State<MarketSectionScreen>
    with StaggeredAnimation, AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  List<String> tabs = [];

  @override
  void initState() {
    super.initState();
    TradingMode currentMode = AppConfig.instance.tradingMode;
    final sysSettingsState = context.read<SysSettingsCubit>().state;

    sysSettingsState.maybeWhen(
      loaded: (_, config) {
        currentMode = TradingModeExtension.fromIndex(config.tradingMode);
      },
      orElse: () {},
    );

    switch (currentMode) {
      case TradingMode.stock:
        tabs = ['stocks', 'stockIndex', 'watchList'];
        break;
      case TradingMode.futures:
        tabs = ['title_futures', 'watchList'];
        break;
      case TradingMode.stockAndFutures:
        tabs = ['stocks', 'stockIndex', 'title_futures', 'watchList'];
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      body: Column(
        children: [
          BlocSelector<MarketCubit, MarketState, MarketSectionTab>(
            selector: (state) => state.selectedMarketSectionTab,
            builder: (context, state) {
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 4.gw),
                child: CommonTabBar(
                  data: tabs,
                  currentIndex: state.index,
                  onTap: (i) => context.read<MarketCubit>().updateMainHeaderTab(MarketSectionTab.values[i]),
                  tabWidth: (1.gsw - 16.gw - 24.gw) / 4,
                  padding: EdgeInsets.all(2),
                  labelPadding: EdgeInsets.symmetric(horizontal: 20.gw),
                  backgroundColor: context.theme.cardColor,
                  radius: 20,
                ),
              );
            },
          ),
          Expanded(
            child: BlocSelector<MarketCubit, MarketState, MarketSectionTab>(
                selector: (state) => state.selectedMarketSectionTab,
                builder: (context, state) => switch (state) {
                      MarketSectionTab.stock => StockScreen(
                          showBackButton: true,
                          scrollController: widget.scrollController,
                        ),
                      MarketSectionTab.stockIndex => IndexTradeScreen(),
                      MarketSectionTab.futures => MultiBlocProvider(
                          providers: [
                            BlocProvider<FTradeListCubit>(
                              create: (_) => FTradeListCubit(FTradeListRepository(), showInHomePage: false),
                            ),
                            BlocProvider<MainCubit>(create: (_) => getIt<MainCubit>()),
                          ],
                          child: FTradeListScreen(showInHomePage: false),
                        ),
                      MarketSectionTab.watchList => WatchListScreen(),
                    }),
          ),
        ],
      ),
    );
  }
}
