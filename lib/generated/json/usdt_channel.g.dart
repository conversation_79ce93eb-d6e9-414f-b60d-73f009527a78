import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/deposit/usdt_channel.dart';

USDTChannelListEntity $USDTChannelListEntityFromJson(Map<String, dynamic> json) {
  final USDTChannelListEntity uSDTChannelListEntity = USDTChannelListEntity();
  final List<USDTChannel>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<USDTChannel>(e) as USDTChannel).toList();
  if (list != null) {
    uSDTChannelListEntity.list = list;
  }
  return uSDTChannelListEntity;
}

Map<String, dynamic> $USDTChannelListEntityToJson(USDTChannelListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension USDTChannelListEntityExtension on USDTChannelListEntity {
  USDTChannelListEntity copyWith({
    List<USDTChannel>? list,
  }) {
    return USDTChannelListEntity()
      ..list = list ?? this.list;
  }
}

USDTChannel $USDTChannelFromJson(Map<String, dynamic> json) {
  final USDTChannel uSDTChannel = USDTChannel();
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    uSDTChannel.currency = currency;
  }
  final double? exchangeRate = jsonConvert.convert<double>(json['exchangeRate']);
  if (exchangeRate != null) {
    uSDTChannel.exchangeRate = exchangeRate;
  }
  final int? giveGiftType = jsonConvert.convert<int>(json['giveGiftType']);
  if (giveGiftType != null) {
    uSDTChannel.giveGiftType = giveGiftType;
  }
  final double? giveRate = jsonConvert.convert<double>(json['giveRate']);
  if (giveRate != null) {
    uSDTChannel.giveRate = giveRate;
  }
  final int? giveRuleType = jsonConvert.convert<int>(json['giveRuleType']);
  if (giveRuleType != null) {
    uSDTChannel.giveRuleType = giveRuleType;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    uSDTChannel.id = id;
  }
  final double? maxAmount = jsonConvert.convert<double>(json['maxAmount']);
  if (maxAmount != null) {
    uSDTChannel.maxAmount = maxAmount;
  }
  final double? minAmount = jsonConvert.convert<double>(json['minAmount']);
  if (minAmount != null) {
    uSDTChannel.minAmount = minAmount;
  }
  final int? network = jsonConvert.convert<int>(json['network']);
  if (network != null) {
    uSDTChannel.network = network;
  }
  final String? networkName = jsonConvert.convert<String>(json['netWorkName']);
  if (networkName != null) {
    uSDTChannel.networkName = networkName;
  }
  final String? rechargeAddress = jsonConvert.convert<String>(json['rechargeAddress']);
  if (rechargeAddress != null) {
    uSDTChannel.rechargeAddress = rechargeAddress;
  }
  final String? rechargeAmountOptions = jsonConvert.convert<String>(json['rechargeAmountOptions']);
  if (rechargeAmountOptions != null) {
    uSDTChannel.rechargeAmountOptions = rechargeAmountOptions;
  }
  final List<String>? rechargeAmountOptionsList = (json['rechargeAmountOptionsList'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (rechargeAmountOptionsList != null) {
    uSDTChannel.rechargeAmountOptionsList = rechargeAmountOptionsList;
  }
  return uSDTChannel;
}

Map<String, dynamic> $USDTChannelToJson(USDTChannel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['currency'] = entity.currency;
  data['exchangeRate'] = entity.exchangeRate;
  data['giveGiftType'] = entity.giveGiftType;
  data['giveRate'] = entity.giveRate;
  data['giveRuleType'] = entity.giveRuleType;
  data['id'] = entity.id;
  data['maxAmount'] = entity.maxAmount;
  data['minAmount'] = entity.minAmount;
  data['network'] = entity.network;
  data['netWorkName'] = entity.networkName;
  data['rechargeAddress'] = entity.rechargeAddress;
  data['rechargeAmountOptions'] = entity.rechargeAmountOptions;
  data['rechargeAmountOptionsList'] = entity.rechargeAmountOptionsList;
  return data;
}

extension USDTChannelExtension on USDTChannel {
  USDTChannel copyWith({
    String? currency,
    double? exchangeRate,
    int? giveGiftType,
    double? giveRate,
    int? giveRuleType,
    int? id,
    double? maxAmount,
    double? minAmount,
    int? network,
    String? networkName,
    String? rechargeAddress,
    String? rechargeAmountOptions,
    List<String>? rechargeAmountOptionsList,
  }) {
    return USDTChannel()
      ..currency = currency ?? this.currency
      ..exchangeRate = exchangeRate ?? this.exchangeRate
      ..giveGiftType = giveGiftType ?? this.giveGiftType
      ..giveRate = giveRate ?? this.giveRate
      ..giveRuleType = giveRuleType ?? this.giveRuleType
      ..id = id ?? this.id
      ..maxAmount = maxAmount ?? this.maxAmount
      ..minAmount = minAmount ?? this.minAmount
      ..network = network ?? this.network
      ..networkName = networkName ?? this.networkName
      ..rechargeAddress = rechargeAddress ?? this.rechargeAddress
      ..rechargeAmountOptions = rechargeAmountOptions ?? this.rechargeAmountOptions
      ..rechargeAmountOptionsList = rechargeAmountOptionsList ?? this.rechargeAmountOptionsList;
  }
}

UsdtRechargeOrder $UsdtRechargeOrderFromJson(Map<String, dynamic> json) {
  final UsdtRechargeOrder usdtRechargeOrder = UsdtRechargeOrder();
  final double? exchangeRate = jsonConvert.convert<double>(json['exchangeRate']);
  if (exchangeRate != null) {
    usdtRechargeOrder.exchangeRate = exchangeRate;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    usdtRechargeOrder.id = id;
  }
  final int? network = jsonConvert.convert<int>(json['network']);
  if (network != null) {
    usdtRechargeOrder.network = network;
  }
  final String? networkName = jsonConvert.convert<String>(json['netWorkName']);
  if (networkName != null) {
    usdtRechargeOrder.networkName = networkName;
  }
  final double? orderAmount = jsonConvert.convert<double>(json['orderAmount']);
  if (orderAmount != null) {
    usdtRechargeOrder.orderAmount = orderAmount;
  }
  final String? orderNo = jsonConvert.convert<String>(json['orderNo']);
  if (orderNo != null) {
    usdtRechargeOrder.orderNo = orderNo;
  }
  final double? orderOriginAmount = jsonConvert.convert<double>(json['orderOriginAmount']);
  if (orderOriginAmount != null) {
    usdtRechargeOrder.orderOriginAmount = orderOriginAmount;
  }
  final String? rechargeAddress = jsonConvert.convert<String>(json['rechargeAddress']);
  if (rechargeAddress != null) {
    usdtRechargeOrder.rechargeAddress = rechargeAddress;
  }
  final double? usdtAmount = jsonConvert.convert<double>(json['usdtAmount']);
  if (usdtAmount != null) {
    usdtRechargeOrder.usdtAmount = usdtAmount;
  }
  return usdtRechargeOrder;
}

Map<String, dynamic> $UsdtRechargeOrderToJson(UsdtRechargeOrder entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['exchangeRate'] = entity.exchangeRate;
  data['id'] = entity.id;
  data['network'] = entity.network;
  data['netWorkName'] = entity.networkName;
  data['orderAmount'] = entity.orderAmount;
  data['orderNo'] = entity.orderNo;
  data['orderOriginAmount'] = entity.orderOriginAmount;
  data['rechargeAddress'] = entity.rechargeAddress;
  data['usdtAmount'] = entity.usdtAmount;
  return data;
}

extension UsdtRechargeOrderExtension on UsdtRechargeOrder {
  UsdtRechargeOrder copyWith({
    double? exchangeRate,
    int? id,
    int? network,
    String? networkName,
    double? orderAmount,
    String? orderNo,
    double? orderOriginAmount,
    String? rechargeAddress,
    double? usdtAmount,
  }) {
    return UsdtRechargeOrder()
      ..exchangeRate = exchangeRate ?? this.exchangeRate
      ..id = id ?? this.id
      ..network = network ?? this.network
      ..networkName = networkName ?? this.networkName
      ..orderAmount = orderAmount ?? this.orderAmount
      ..orderNo = orderNo ?? this.orderNo
      ..orderOriginAmount = orderOriginAmount ?? this.orderOriginAmount
      ..rechargeAddress = rechargeAddress ?? this.rechargeAddress
      ..usdtAmount = usdtAmount ?? this.usdtAmount;
  }
}